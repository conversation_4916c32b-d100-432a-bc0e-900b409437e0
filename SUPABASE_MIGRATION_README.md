# 🚀 ALORF BIOMED Supabase Migration Guide

This guide provides step-by-step instructions for migrating the ALORF BIOMED application from JSON file storage to Supabase PostgreSQL database.

## 📋 Prerequisites

- Python 3.8 or higher
- Active internet connection
- GitHub account (for Supabase signup)
- Current ALORF BIOMED application running

## 🎯 Migration Overview

**Current System**: JSON files (`ppm.json`, `ocm.json`, `training.json`, etc.)
**Target System**: Supabase PostgreSQL database
**Estimated Time**: 2-4 hours
**Data Volume**: ~2,000+ records across all tables

## 📝 Step-by-Step Migration Process

### Step 1: Setup and Preparation

#### 1.1 Install Dependencies
```bash
# Run the setup script
python setup_supabase.py
```

This will:
- Install required Python packages (SQLAlchemy, psycopg2, supabase)
- Create backup of current data files
- Verify installation

#### 1.2 Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Sign up/login with your GitHub account
3. Click "New Project"
4. Fill in project details:
   - **Name**: `alorf-biomed-system`
   - **Database Password**: Generate strong password (SAVE THIS!)
   - **Region**: Choose closest to your location
5. Click "Create new project"
6. Wait 2-3 minutes for project setup

#### 1.3 Get Database Credentials
From your Supabase dashboard:

1. Go to **Settings** → **Database**
2. Note down these values:
   - **Host**: `db.xxx.supabase.co`
   - **Database name**: `postgres`
   - **Port**: `5432`
   - **User**: `postgres`
   - **Password**: (the one you set)

3. Go to **Settings** → **API**
4. Note down:
   - **Project URL**: `https://xxx.supabase.co`
   - **anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

#### 1.4 Update Environment Variables
Edit your `.env` file and replace the placeholder values:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_KEY=your-service-role-key-here
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Migration Control
USE_SUPABASE=false  # Keep false until migration is complete
```

### Step 2: Test Connection

```bash
# Test your Supabase connection
python test_supabase_connection.py
```

**Expected Output:**
```
✅ Environment Variables............ PASSED
✅ Supabase Client.................. PASSED  
✅ PostgreSQL Connection............ PASSED
✅ Database Manager................. PASSED
📈 Overall: 4/4 tests passed
🎉 All tests passed! Supabase is ready for migration.
```

If any tests fail, check your credentials and try again.

### Step 3: Create Database Schema

```bash
# Create all required tables in Supabase
python create_supabase_schema.py
```

**Expected Output:**
```
📋 Creating table: departments
✅ Table 'departments' created successfully
📋 Creating table: trainers
✅ Table 'trainers' created successfully
...
📊 Schema Creation Summary: 11/11 tables created
🎉 Database schema created successfully!
```

### Step 4: Migrate Data

```bash
# Migrate all JSON data to Supabase
python migrate_to_supabase.py
```

**Expected Output:**
```
🏢 Migrating departments...
✅ Departments migration completed: 27/27
👨‍🏫 Migrating trainers...
✅ Trainers migration completed: 10/10
🔧 Migrating PPM equipment...
✅ PPM equipment migration completed: 1000+/1000+
...
📊 MIGRATION SUMMARY
departments................. ✅  27/27   (100.0%)
trainers.................... ✅  10/10   (100.0%)
ppm_equipment............... ✅ 1000+/1000+ (100.0%)
...
🎉 Migration completed and verified successfully!
```

### Step 5: Switch to Supabase

If migration was successful, switch the application to use Supabase:

```bash
# Update .env file
USE_SUPABASE=true
```

Or run this Python command:
```python
from app.database import db_manager
db_manager.switch_to_supabase()
```

### Step 6: Test Application

1. Restart your Flask application
2. Test all major functions:
   - View PPM equipment list
   - View OCM equipment list
   - View training records
   - Add new equipment
   - Edit existing records
   - Department management
   - Trainer management

## 🔧 Troubleshooting

### Common Issues

#### Connection Errors
```
❌ PostgreSQL connection failed: connection to server failed
```
**Solution**: Check your DATABASE_URL and ensure Supabase project is running.

#### Authentication Errors
```
❌ Supabase client connection failed: Invalid API key
```
**Solution**: Verify SUPABASE_ANON_KEY in your .env file.

#### Migration Errors
```
❌ Failed to migrate PPM equipment: duplicate key value
```
**Solution**: Clear existing data in Supabase and re-run migration.

#### Schema Creation Errors
```
❌ Failed to create table 'departments': relation already exists
```
**Solution**: This is normal if re-running. The script handles existing tables.

### Rollback Procedure

If you need to rollback to JSON files:

```bash
# Switch back to JSON files
python -c "from app.database import db_manager; db_manager.switch_to_sqlite()"
```

Or manually update `.env`:
```env
USE_SUPABASE=false
```

## 📊 Data Verification

After migration, verify your data:

### Check Record Counts
```sql
-- In Supabase SQL Editor
SELECT 
    'departments' as table_name, COUNT(*) as records FROM departments
UNION ALL
SELECT 'trainers', COUNT(*) FROM trainers
UNION ALL  
SELECT 'ppm_equipment', COUNT(*) FROM ppm_equipment
UNION ALL
SELECT 'ocm_equipment', COUNT(*) FROM ocm_equipment
UNION ALL
SELECT 'training_records', COUNT(*) FROM training_records;
```

### Sample Data Check
```sql
-- Check PPM equipment sample
SELECT department, name, model, serial, manufacturer 
FROM ppm_equipment 
LIMIT 5;

-- Check departments
SELECT id, department_name, information 
FROM departments 
ORDER BY id;
```

## 🎉 Post-Migration

### Performance Optimization
1. **Indexes**: All important indexes are created automatically
2. **Connection Pooling**: Configured in database.py
3. **Query Optimization**: Monitor slow queries in Supabase dashboard

### Monitoring
1. **Supabase Dashboard**: Monitor database performance
2. **Application Logs**: Check for any database errors
3. **Backup Strategy**: Set up regular backups in Supabase

### Security
1. **Row Level Security**: Consider enabling RLS for sensitive data
2. **API Keys**: Rotate keys periodically
3. **Database Access**: Limit direct database access

## 📞 Support

If you encounter issues:

1. **Check Logs**: Review migration logs for specific errors
2. **Verify Credentials**: Ensure all Supabase credentials are correct
3. **Test Connection**: Re-run connection tests
4. **Rollback**: Use rollback procedure if needed

## 🎯 Success Criteria

Migration is successful when:
- ✅ All tests pass in `test_supabase_connection.py`
- ✅ All tables created in `create_supabase_schema.py`
- ✅ All data migrated in `migrate_to_supabase.py`
- ✅ Application functions normally with `USE_SUPABASE=true`
- ✅ No data loss or corruption detected

**Congratulations! Your ALORF BIOMED system is now running on Supabase! 🎉**
