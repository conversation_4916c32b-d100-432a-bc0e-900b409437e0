"""
Department service for managing department data.
Supports both JSON files and Supabase database.
"""
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from app.config import Config
from app.models.department import Department, DepartmentCreate, DepartmentUpdate
from app.services.base_service import BaseService

logger = logging.getLogger(__name__)

class DepartmentService(BaseService):
    """Service for managing department data."""

    def __init__(self):
        super().__init__(
            json_file_path='data/departments.json',
            table_name='departments'
        )
    
    def get_all(self) -> List[Dict[str, Any]]:
        """Get all departments."""
        if self.use_database:
            return self._fetch_all("SELECT * FROM departments ORDER BY department_name")
        else:
            return self._load_json_data()

    def get_by_id(self, department_id: Union[int, str]) -> Optional[Dict[str, Any]]:
        """Get a department by ID."""
        if self.use_database:
            return self._fetch_one(
                "SELECT * FROM departments WHERE id = :id",
                {'id': int(department_id)}
            )
        else:
            data = self._load_json_data()
            for dept in data:
                if dept.get('id') == int(department_id):
                    return dept
            return None

    def get_by_name(self, department_name: str) -> Optional[Dict[str, Any]]:
        """Get a department by name."""
        if self.use_database:
            return self._fetch_one(
                "SELECT * FROM departments WHERE department_name = :name",
                {'name': department_name}
            )
        else:
            data = self._load_json_data()
            for dept in data:
                if dept.get('department_name') == department_name:
                    return dept
            return None
    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new department."""
        try:
            # Validate input data
            dept_create = DepartmentCreate(**data)

            if self.use_database:
                # Check if department name already exists
                existing = self.get_by_name(dept_create.department_name)
                if existing:
                    logger.error(f"Department '{dept_create.department_name}' already exists")
                    return None

                # Insert into database
                query = """
                    INSERT INTO departments (department_name, information, created_date, updated_date)
                    VALUES (:department_name, :information, NOW(), NOW())
                    RETURNING id, department_name, information, created_date, updated_date
                """

                result = self._execute_query(query, {
                    'department_name': dept_create.department_name,
                    'information': dept_create.information
                })

                row = result.fetchone()
                if row:
                    columns = result.keys()
                    return dict(zip(columns, row))
                return None

            else:
                # JSON file operations
                json_data = self._load_json_data()

                # Check if department name already exists
                for dept in json_data:
                    if dept.get('department_name') == dept_create.department_name:
                        logger.error(f"Department '{dept_create.department_name}' already exists")
                        return None

                # Create new department
                new_dept = {
                    'id': self._generate_id(json_data),
                    'department_name': dept_create.department_name,
                    'information': dept_create.information,
                    'created_date': datetime.now().isoformat(),
                    'updated_date': datetime.now().isoformat()
                }

                json_data.append(new_dept)

                if self._save_json_data(json_data):
                    return new_dept
                return None

        except Exception as e:
            logger.error(f"Failed to create department: {e}")
            return None
    def update(self, department_id: Union[int, str], data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an existing department."""
        try:
            # Validate input data
            dept_update = DepartmentUpdate(**data)

            if self.use_database:
                # Check if department exists
                existing = self.get_by_id(department_id)
                if not existing:
                    logger.error(f"Department with ID {department_id} not found")
                    return None

                # Check if new name conflicts with existing department
                if dept_update.department_name:
                    name_conflict = self.get_by_name(dept_update.department_name)
                    if name_conflict and name_conflict['id'] != int(department_id):
                        logger.error(f"Department name '{dept_update.department_name}' already exists")
                        return None

                # Build update query
                update_fields = []
                params = {'id': int(department_id)}

                if dept_update.department_name is not None:
                    update_fields.append("department_name = :department_name")
                    params['department_name'] = dept_update.department_name

                if dept_update.information is not None:
                    update_fields.append("information = :information")
                    params['information'] = dept_update.information

                update_fields.append("updated_date = NOW()")

                query = f"""
                    UPDATE departments
                    SET {', '.join(update_fields)}
                    WHERE id = :id
                    RETURNING id, department_name, information, created_date, updated_date
                """

                result = self._execute_query(query, params)
                row = result.fetchone()
                if row:
                    columns = result.keys()
                    return dict(zip(columns, row))
                return None

            else:
                # JSON file operations
                json_data = self._load_json_data()

                for i, dept in enumerate(json_data):
                    if dept.get('id') == int(department_id):
                        # Check if new name conflicts
                        if dept_update.department_name:
                            for other_dept in json_data:
                                if (other_dept.get('department_name') == dept_update.department_name and
                                    other_dept.get('id') != int(department_id)):
                                    logger.error(f"Department name '{dept_update.department_name}' already exists")
                                    return None

                        # Update fields
                        if dept_update.department_name is not None:
                            dept['department_name'] = dept_update.department_name
                        if dept_update.information is not None:
                            dept['information'] = dept_update.information

                        dept['updated_date'] = datetime.now().isoformat()

                        if self._save_json_data(json_data):
                            return dept
                        return None

                logger.error(f"Department with ID {department_id} not found")
                return None

        except Exception as e:
            logger.error(f"Failed to update department: {e}")
            return None
    def delete(self, department_id: Union[int, str]) -> bool:
        """Delete a department."""
        try:
            if self.use_database:
                # Check if department exists
                existing = self.get_by_id(department_id)
                if not existing:
                    logger.error(f"Department with ID {department_id} not found")
                    return False

                # Check if department is referenced by trainers
                trainers_count = self._fetch_one(
                    "SELECT COUNT(*) as count FROM trainers WHERE department_id = :id",
                    {'id': int(department_id)}
                )

                if trainers_count and trainers_count['count'] > 0:
                    logger.error(f"Cannot delete department: {trainers_count['count']} trainers are assigned to it")
                    return False

                # Delete department
                result = self._execute_query(
                    "DELETE FROM departments WHERE id = :id",
                    {'id': int(department_id)}
                )

                return result.rowcount > 0

            else:
                # JSON file operations
                json_data = self._load_json_data()

                for i, dept in enumerate(json_data):
                    if dept.get('id') == int(department_id):
                        # Check if department is referenced (would need to check trainers.json)
                        # For now, just delete
                        json_data.pop(i)
                        return self._save_json_data(json_data)

                logger.error(f"Department with ID {department_id} not found")
                return False

        except Exception as e:
            logger.error(f"Failed to delete department: {e}")
            return False
    def get_department_options(self) -> List[Dict[str, Any]]:
        """Get departments formatted for dropdown options."""
        departments = self.get_all()
        return [
            {
                'id': dept['id'],
                'name': dept['department_name'],
                'value': dept['department_name']
            }
            for dept in sorted(departments, key=lambda x: x['department_name'])
        ]

    def search_departments(self, query: str) -> List[Dict[str, Any]]:
        """Search departments by name or information."""
        if self.use_database:
            return self._fetch_all(
                """
                SELECT * FROM departments
                WHERE department_name ILIKE :query OR information ILIKE :query
                ORDER BY department_name
                """,
                {'query': f'%{query}%'}
            )
        else:
            data = self._load_json_data()
            query_lower = query.lower()
            return [
                dept for dept in data
                if (query_lower in dept.get('department_name', '').lower() or
                    query_lower in dept.get('information', '').lower())
            ]

    def get_statistics(self) -> Dict[str, Any]:
        """Get department statistics."""
        total_departments = self.count()

        if self.use_database:
            # Get departments with trainer counts
            dept_stats = self._fetch_all("""
                SELECT d.id, d.department_name, COUNT(t.id) as trainer_count
                FROM departments d
                LEFT JOIN trainers t ON d.id = t.department_id
                GROUP BY d.id, d.department_name
                ORDER BY trainer_count DESC
            """)
        else:
            # For JSON files, we'd need to load trainers.json and count
            dept_stats = []
            departments = self.get_all()
            for dept in departments:
                dept_stats.append({
                    'id': dept['id'],
                    'department_name': dept['department_name'],
                    'trainer_count': 0  # Would need to implement trainer counting
                })

        return {
            'total_departments': total_departments,
            'department_stats': dept_stats
        }

    # Legacy methods for backward compatibility
    @staticmethod
    def get_departments_for_dropdown() -> List[Dict[str, Any]]:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        return service.get_department_options()

    @staticmethod
    def get_all_departments() -> List[Department]:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        data = service.get_all()
        return [Department.from_dict(dept_data) for dept_data in data]

    @staticmethod
    def get_department_by_id(department_id: int) -> Optional[Department]:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        dept_data = service.get_by_id(department_id)
        return Department.from_dict(dept_data) if dept_data else None

    @staticmethod
    def get_department_by_name(department_name: str) -> Optional[Department]:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        dept_data = service.get_by_name(department_name)
        return Department.from_dict(dept_data) if dept_data else None

    @staticmethod
    def create_department(department_data: DepartmentCreate) -> Department:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        dept_dict = service.create(department_data.dict())
        if dept_dict:
            return Department.from_dict(dept_dict)
        raise ValueError("Failed to create department")

    @staticmethod
    def update_department(department_id: int, update_data: DepartmentUpdate) -> Optional[Department]:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        dept_dict = service.update(department_id, update_data.dict(exclude_unset=True))
        return Department.from_dict(dept_dict) if dept_dict else None

    @staticmethod
    def delete_department(department_id: int) -> bool:
        """Legacy method for backward compatibility."""
        service = DepartmentService()
        return service.delete(department_id)
