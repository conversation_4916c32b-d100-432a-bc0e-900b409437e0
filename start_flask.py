#!/usr/bin/env python3
"""
Simple Flask application starter for testing Supabase migration.
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def start_app():
    """Start the Flask application."""
    try:
        from app import create_app
        
        print("🚀 Starting ALORF BIOMED Flask Application")
        print("=" * 50)
        print(f"✅ Supabase Mode: {os.getenv('USE_SUPABASE', 'false')}")
        print(f"✅ Database URL: {os.getenv('DATABASE_URL', 'Not set')[:50]}...")
        print("=" * 50)
        
        # Create the Flask app
        app = create_app()
        
        # Get port from environment or use default
        port = int(os.environ.get("PORT", 5001))
        
        print(f"🌐 Starting server on http://localhost:{port}")
        print("📊 Ready to test Supabase migration!")
        print("=" * 50)
        
        # Start the Flask development server
        app.run(
            debug=True,
            host="0.0.0.0",
            port=port,
            use_reloader=False  # Disable reloader to avoid issues
        )
        
    except Exception as e:
        print(f"❌ Failed to start Flask application: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    start_app()
