[{"id": 1, "timestamp": "2025-06-22 10:15:30", "event_type": "System Startup", "performed_by": "System", "description": "Hospital Equipment Management System started successfully", "status": "Success", "details": {"version": "1.0.0", "modules": ["PPM", "OCM", "Training", "Notifications"]}}, {"id": 2, "timestamp": "2025-06-22 10:30:45", "event_type": "Equipment Added", "performed_by": "Admin User", "description": "New PPM equipment added: Ventilator (Serial: VNT-2025-001)", "status": "Success", "details": {"equipment_type": "PPM", "serial": "VNT-2025-001", "department": "ICU"}}, {"id": 3, "timestamp": "2025-06-22 11:45:12", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 15 equipment maintenance tasks (7-day threshold)", "status": "Success", "details": {"threshold": "7 days", "equipment_count": 15, "recipient": "<EMAIL>"}}, {"id": 4, "timestamp": "2025-06-22 14:20:18", "event_type": "Bulk Import", "performed_by": "Maintenance Manager", "description": "Bulk import of 25 OCM equipment records from CSV file", "status": "Success", "details": {"file_type": "CSV", "records_imported": 25, "records_failed": 0, "equipment_type": "OCM"}}, {"id": 5, "timestamp": "2025-06-22 15:10:33", "event_type": "Setting Changed", "performed_by": "Admin User", "description": "Push notification interval changed from 60 to 5 minutes", "status": "Success", "details": {"setting": "push_notification_interval_minutes", "old_value": 60, "new_value": 5}}, {"id": 6, "timestamp": "2025-06-22 16:31:05", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 7, "timestamp": "2025-06-22 16:31:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 8, "timestamp": "2025-06-22 16:31:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 9, "timestamp": "2025-06-22 16:31:07", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 10, "timestamp": "2025-06-22 16:34:14", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 11, "timestamp": "2025-06-22 16:34:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 12, "timestamp": "2025-06-22 16:34:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 13, "timestamp": "2025-06-22 16:34:17", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 14, "timestamp": "2025-06-22 16:34:18", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 15, "timestamp": "2025-06-22 16:34:18", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 16, "timestamp": "2025-06-22 16:34:19", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 17, "timestamp": "2025-06-22 16:35:25", "event_type": "Data Export", "performed_by": "User", "description": "Exported 16 audit log entries to CSV", "status": "Success", "details": {"export_format": "CSV", "record_count": 16}}, {"id": 18, "timestamp": "2025-06-22 16:35:50", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 19, "timestamp": "2025-06-22 16:35:50", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 20, "timestamp": "2025-06-22 16:35:51", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 21, "timestamp": "2025-06-22 16:35:52", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 22, "timestamp": "2025-06-22 16:36:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 23, "timestamp": "2025-06-22 16:36:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 24, "timestamp": "2025-06-22 16:36:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 25, "timestamp": "2025-06-22 16:36:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 26, "timestamp": "2025-06-22 16:38:54", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 27, "timestamp": "2025-06-22 16:38:55", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 28, "timestamp": "2025-06-22 16:38:56", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 29, "timestamp": "2025-06-22 16:41:51", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 30, "timestamp": "2025-06-22 16:41:52", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 31, "timestamp": "2025-06-22 16:41:53", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 32, "timestamp": "2025-06-22 16:41:53", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 33, "timestamp": "2025-06-22 16:41:53", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 34, "timestamp": "2025-06-22 16:41:54", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 35, "timestamp": "2025-06-22 16:41:54", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 36, "timestamp": "2025-06-22 16:42:09", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 37, "timestamp": "2025-06-22 16:42:10", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 38, "timestamp": "2025-06-22 16:42:10", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 39, "timestamp": "2025-06-22 16:51:00", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 40, "timestamp": "2025-06-22 16:51:01", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 41, "timestamp": "2025-06-22 16:51:02", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 42, "timestamp": "2025-06-22 17:06:24", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 43, "timestamp": "2025-06-22 17:06:25", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 44, "timestamp": "2025-06-22 17:06:26", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 45, "timestamp": "2025-06-22 17:14:58", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 46, "timestamp": "2025-06-22 17:14:59", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 47, "timestamp": "2025-06-22 17:14:59", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 48, "timestamp": "2025-06-22 17:39:56", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250622_173956.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250622_173956.json", "size_kb": 0.71, "settings_count": 12}}, {"id": 50, "timestamp": "2025-06-22 17:40:41", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup failed", "status": "Failed", "details": {"backup_type": "full", "error": "File size too large, try using force_zip64"}}, {"id": 51, "timestamp": "2025-06-22 18:58:14", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 52, "timestamp": "2025-06-22 18:58:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 53, "timestamp": "2025-06-22 18:58:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 54, "timestamp": "2025-06-22 19:05:04", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250622_173956.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250622_173956.json"}}, {"id": 55, "timestamp": "2025-06-22 19:07:14", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 56, "timestamp": "2025-06-22 19:07:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 57, "timestamp": "2025-06-22 19:07:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 58, "timestamp": "2025-06-23 11:18:45", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 59, "timestamp": "2025-06-25 20:36:05", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 60, "timestamp": "2025-06-26 14:39:23", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 61, "timestamp": "2025-06-26 14:46:40", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 62, "timestamp": "2025-06-26 15:02:38", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 63, "timestamp": "2025-06-27 02:20:41", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250627_022040.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_022040.json", "size_kb": 2.6, "settings_count": 14}}, {"id": 64, "timestamp": "2025-06-27 02:20:47", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250627_022040.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_022040.json"}}, {"id": 65, "timestamp": "2025-06-27 02:30:47", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup failed", "status": "Failed", "details": {"backup_type": "full", "error": "[<PERSON><PERSON><PERSON> 28] No space left on device"}}, {"id": 66, "timestamp": "2025-06-27 14:31:49", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 67, "timestamp": "2025-06-27 14:31:50", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 68, "timestamp": "2025-06-27 14:31:51", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 69, "timestamp": "2025-06-27 16:46:55", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 70, "timestamp": "2025-06-27 16:46:56", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 71, "timestamp": "2025-06-27 16:46:56", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 72, "timestamp": "2025-06-27 17:03:16", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250627_170316.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_170316.json", "size_kb": 0.68, "settings_count": 12}}, {"id": 73, "timestamp": "2025-06-27 17:16:10", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250627_171610.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250627_171610.zip", "size_mb": 0.15, "files_backed_up": 20, "compression_level": 9}}, {"id": 74, "timestamp": "2025-06-27 17:16:14", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250627_171614.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_171614.json", "size_kb": 0.74, "settings_count": 12}}, {"id": 75, "timestamp": "2025-06-27 17:18:10", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250627_171810.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250627_171810.zip", "size_mb": 0.15, "files_backed_up": 20, "compression_level": 9}}, {"id": 76, "timestamp": "2025-06-27 17:18:15", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250627_171815.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_171815.json", "size_kb": 0.82, "settings_count": 15}}, {"id": 77, "timestamp": "2025-06-27 21:06:42", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250627_171815.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_171815.json"}}, {"id": 78, "timestamp": "2025-06-27 21:06:48", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250627_171810.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250627_171810.zip"}}, {"id": 79, "timestamp": "2025-06-27 21:06:55", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250627_171614.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_171614.json"}}, {"id": 80, "timestamp": "2025-06-27 21:07:00", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250627_171610.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250627_171610.zip"}}, {"id": 81, "timestamp": "2025-06-27 21:07:05", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250627_170316.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250627_170316.json"}}, {"id": 82, "timestamp": "2025-06-27 23:56:50", "event_type": "Backup Restored", "performed_by": "User", "description": "Full application backup restored successfully", "status": "Success", "details": {"backup_type": "full", "restored_items": 16, "previous_data_backup": "data_before_restore_20250627_235649"}}, {"id": 83, "timestamp": "2025-06-28 14:24:31", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "<EMAIL>", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 84, "timestamp": "2025-06-28 14:35:20", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip", "size_mb": 0.15, "files_backed_up": 20, "compression_level": 9}}, {"id": 85, "timestamp": "2025-06-28 14:35:23", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250628_143523.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250628_143523.json", "size_kb": 0.82, "settings_count": 15}}, {"id": 86, "timestamp": "2025-06-28 14:35:32", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 87, "timestamp": "2025-06-28 14:38:09", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "<EMAIL>", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 88, "timestamp": "2025-06-28 14:45:51", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 89, "timestamp": "2025-06-28 14:45:52", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 90, "timestamp": "2025-06-28 14:45:52", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 91, "timestamp": "2025-06-28 14:45:53", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 92, "timestamp": "2025-06-28 15:50:46", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 93, "timestamp": "2025-06-28 15:50:47", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 94, "timestamp": "2025-06-28 15:53:28", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "<EMAIL>", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 95, "timestamp": "2025-06-28 16:01:22", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 96, "timestamp": "2025-06-28 16:01:23", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 97, "timestamp": "2025-06-28 16:01:47", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "<EMAIL>", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 98, "timestamp": "2025-06-28 16:02:30", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 99, "timestamp": "2025-06-28 16:02:31", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 100, "timestamp": "2025-06-28 16:02:31", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 101, "timestamp": "2025-06-28 16:02:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 102, "timestamp": "2025-06-28 16:04:55", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 103, "timestamp": "2025-06-28 16:04:56", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 104, "timestamp": "2025-06-28 16:04:57", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 105, "timestamp": "2025-06-28 16:04:58", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 106, "timestamp": "2025-06-28 16:11:04", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 107, "timestamp": "2025-06-28 16:11:04", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 108, "timestamp": "2025-06-28 16:11:05", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 109, "timestamp": "2025-06-28 16:11:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 110, "timestamp": "2025-06-28 16:16:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 111, "timestamp": "2025-06-28 16:16:35", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 112, "timestamp": "2025-06-28 16:16:35", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 113, "timestamp": "2025-06-28 16:16:36", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 114, "timestamp": "2025-06-28 16:16:37", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 115, "timestamp": "2025-06-28 16:16:37", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 116, "timestamp": "2025-06-28 16:16:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 117, "timestamp": "2025-06-28 16:16:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 118, "timestamp": "2025-06-28 16:18:30", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 119, "timestamp": "2025-06-28 16:18:30", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 120, "timestamp": "2025-06-28 16:18:31", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 121, "timestamp": "2025-06-28 16:18:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 122, "timestamp": "2025-06-28 16:18:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 123, "timestamp": "2025-06-28 16:18:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 124, "timestamp": "2025-06-28 16:18:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 125, "timestamp": "2025-06-28 16:18:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 126, "timestamp": "2025-06-28 16:19:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 127, "timestamp": "2025-06-28 16:19:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 128, "timestamp": "2025-06-28 16:19:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 129, "timestamp": "2025-06-28 16:19:35", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 130, "timestamp": "2025-06-28 16:19:36", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 131, "timestamp": "2025-06-28 16:19:37", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 132, "timestamp": "2025-06-28 16:19:37", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 133, "timestamp": "2025-06-28 16:19:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 134, "timestamp": "2025-06-28 16:19:39", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "", "sender": "dr.vet.waled<PERSON><PERSON><EMAIL>"}}, {"id": 135, "timestamp": "2025-06-28 16:19:40", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 136, "timestamp": "2025-06-28 16:19:40", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 137, "timestamp": "2025-06-28 16:19:41", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 138, "timestamp": "2025-06-28 16:19:42", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 139, "timestamp": "2025-06-28 16:20:22", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 140, "timestamp": "2025-06-28 16:20:23", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 141, "timestamp": "2025-06-28 16:20:24", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 142, "timestamp": "2025-06-28 16:20:24", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 143, "timestamp": "2025-06-28 16:25:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 144, "timestamp": "2025-06-28 16:25:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 145, "timestamp": "2025-06-28 16:25:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 146, "timestamp": "2025-06-28 16:25:35", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 147, "timestamp": "2025-06-28 16:31:17", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 148, "timestamp": "2025-06-28 16:31:18", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 149, "timestamp": "2025-06-28 16:31:19", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 150, "timestamp": "2025-06-28 16:31:19", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 151, "timestamp": "2025-06-29 02:39:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 152, "timestamp": "2025-06-29 02:39:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 153, "timestamp": "2025-06-29 02:39:40", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 154, "timestamp": "2025-06-29 02:39:41", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 155, "timestamp": "2025-06-29 02:43:51", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 156, "timestamp": "2025-06-29 02:43:51", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 157, "timestamp": "2025-06-29 02:43:52", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 158, "timestamp": "2025-06-29 02:43:53", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 159, "timestamp": "2025-06-29 03:41:56", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 160, "timestamp": "2025-06-29 03:41:57", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 161, "timestamp": "2025-06-29 03:41:58", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 162, "timestamp": "2025-06-29 03:41:58", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 163, "timestamp": "2025-06-29 16:44:54", "event_type": "Equipment History Added", "performed_by": "testuser", "description": "Added history note to PPM equipment TEST001", "status": "Success", "details": {"equipment_id": "TEST001", "equipment_type": "ppm", "note_id": "e134339c-a8c9-4251-a7ff-67e0383f01af", "note_length": 49}}, {"id": 164, "timestamp": "2025-06-29 16:44:54", "event_type": "Equipment History Deleted", "performed_by": "System", "description": "Deleted history note from PPM equipment TEST001", "status": "Success", "details": {"equipment_id": "TEST001", "equipment_type": "ppm", "note_id": "e134339c-a8c9-4251-a7ff-67e0383f01af", "attachments_deleted": 0}}, {"id": 165, "timestamp": "2025-06-29 16:44:54", "event_type": "Equipment History Added", "performed_by": "testuser", "description": "Test history event", "status": "Success", "details": {"test": true}}, {"id": 166, "timestamp": "2025-06-29 16:52:13", "event_type": "Equipment History Added", "performed_by": "test_user", "description": "Added history note to PPM equipment TEST-FIX-001", "status": "Success", "details": {"equipment_id": "TEST-FIX-001", "equipment_type": "ppm", "note_id": "70c6f9bb-1134-4933-8821-541003d204a4", "note_length": 58}}, {"id": 167, "timestamp": "2025-06-29 16:52:13", "event_type": "Equipment History Deleted", "performed_by": "System", "description": "Deleted history note from PPM equipment TEST-FIX-001", "status": "Success", "details": {"equipment_id": "TEST-FIX-001", "equipment_type": "ppm", "note_id": "70c6f9bb-1134-4933-8821-541003d204a4", "attachments_deleted": 0}}, {"id": 168, "timestamp": "2025-06-29 17:20:57", "event_type": "Equipment History Added", "performed_by": "admin", "description": "Added history note to OCM equipment SW3266#", "status": "Success", "details": {"equipment_id": "SW3266#", "equipment_type": "ocm", "note_id": "a7231391-3d83-49ae-8a5e-c954b55a163c", "note_length": 11}}, {"id": 169, "timestamp": "2025-06-29 17:20:57", "event_type": "History Attachment Added", "performed_by": "admin", "description": "Added attachment 's-l1600.jpg' to history note", "status": "Success", "details": {"note_id": "a7231391-3d83-49ae-8a5e-c954b55a163c", "attachment_id": "0850913f-e392-403e-83e6-c248f3e96196", "filename": "s-l1600.jpg", "file_size": 4096}}, {"id": 170, "timestamp": "2025-06-29 17:20:57", "event_type": "History Attachment Added", "performed_by": "admin", "description": "Added attachment 'al_manar_clinic_invoice.pdf' to history note", "status": "Success", "details": {"note_id": "a7231391-3d83-49ae-8a5e-c954b55a163c", "attachment_id": "5167d765-86e9-455c-85d1-e46aeb00ec70", "filename": "al_manar_clinic_invoice.pdf", "file_size": 224668}}, {"id": 171, "timestamp": "2025-06-29 17:20:57", "event_type": "Equipment History Added", "performed_by": "admin", "description": "History Added OCM equipment: SW3266#", "status": "Success", "details": {"equipment_type": "OCM", "serial": "SW3266#", "action": "History Added"}}, {"id": 172, "timestamp": "2025-06-29 19:49:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 173, "timestamp": "2025-06-29 19:49:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 174, "timestamp": "2025-06-29 19:49:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 175, "timestamp": "2025-06-29 19:49:40", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 176, "timestamp": "2025-06-29 19:57:01", "event_type": "Equipment History Added", "performed_by": "admin", "description": "Added history note to OCM equipment SW3266-hash", "status": "Success", "details": {"equipment_id": "SW3266-hash", "equipment_type": "ocm", "note_id": "c63e3d1a-5997-49ff-ba1c-14224fbb4fd3", "note_length": 29}}, {"id": 177, "timestamp": "2025-06-29 19:57:01", "event_type": "History Attachment Added", "performed_by": "admin", "description": "Added attachment '1.jpg' to history note", "status": "Success", "details": {"note_id": "c63e3d1a-5997-49ff-ba1c-14224fbb4fd3", "attachment_id": "27aa96c2-9b51-47ef-9f79-644c32a32282", "filename": "1.jpg", "file_size": 28791}}, {"id": 178, "timestamp": "2025-06-29 19:57:01", "event_type": "History Attachment Added", "performed_by": "admin", "description": "Added attachment 'gata_cheats.docx' to history note", "status": "Success", "details": {"note_id": "c63e3d1a-5997-49ff-ba1c-14224fbb4fd3", "attachment_id": "f062cf6b-6727-483e-a38c-636dc5df4c54", "filename": "gata_cheats.docx", "file_size": 15998}}, {"id": 179, "timestamp": "2025-06-29 19:57:01", "event_type": "Equipment History Added", "performed_by": "admin", "description": "History Added OCM equipment: SW3266-hash", "status": "Success", "details": {"equipment_type": "OCM", "serial": "SW3266-hash", "action": "History Added"}}, {"id": 180, "timestamp": "2025-06-29 20:02:59", "event_type": "Equipment History Deleted", "performed_by": "System", "description": "Deleted history note from OCM equipment SW3266-hash", "status": "Success", "details": {"equipment_id": "SW3266-hash", "equipment_type": "ocm", "note_id": "c63e3d1a-5997-49ff-ba1c-14224fbb4fd3", "attachments_deleted": 2}}, {"id": 181, "timestamp": "2025-06-29 20:02:59", "event_type": "Equipment History Deleted", "performed_by": "admin", "description": "History Deleted OCM equipment: SW3266-hash", "status": "Success", "details": {"equipment_type": "OCM", "serial": "SW3266-hash", "action": "History Deleted"}}, {"id": 182, "timestamp": "2025-06-29 22:41:03", "event_type": "Equipment History Deleted", "performed_by": "System", "description": "Deleted history note from OCM equipment SW3266#", "status": "Success", "details": {"equipment_id": "SW3266#", "equipment_type": "ocm", "note_id": "a7231391-3d83-49ae-8a5e-c954b55a163c", "attachments_deleted": 2}}, {"id": 183, "timestamp": "2025-06-29 22:41:03", "event_type": "Equipment History Deleted", "performed_by": "admin", "description": "History Deleted OCM equipment: SW3266#", "status": "Success", "details": {"equipment_type": "OCM", "serial": "SW3266#", "action": "History Deleted"}}, {"id": 184, "timestamp": "2025-06-29 22:47:46", "event_type": "Equipment History Added", "performed_by": "admin", "description": "Added history note to PPM equipment 111632045", "status": "Success", "details": {"equipment_id": "111632045", "equipment_type": "ppm", "note_id": "ab0b75f1-74da-465c-8788-8e715cc7e708", "note_length": 79}}, {"id": 185, "timestamp": "2025-06-29 22:47:46", "event_type": "History Attachment Added", "performed_by": "admin", "description": "Added attachment 'incident_related_medical_device.pdf' to history note", "status": "Success", "details": {"note_id": "ab0b75f1-74da-465c-8788-8e715cc7e708", "attachment_id": "a2641a88-38bc-4e75-b7aa-2ae5b5d54554", "filename": "incident_related_medical_device.pdf", "file_size": 2708815}}, {"id": 186, "timestamp": "2025-06-29 22:47:46", "event_type": "Equipment History Added", "performed_by": "admin", "description": "History Added PPM equipment: 111632045", "status": "Success", "details": {"equipment_type": "PPM", "serial": "111632045", "action": "History Added"}}, {"id": 187, "timestamp": "2025-06-29 23:19:05", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 4 equipment maintenance tasks (1-day threshold)", "status": "Failed", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "error_code": 401, "error_response": {"ErrorIdentifier": "a48aae63-1a63-491b-955e-bb30afc86218", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 188, "timestamp": "2025-06-29 23:19:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 12 equipment maintenance tasks (7-day threshold)", "status": "Failed", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "error_code": 401, "error_response": {"ErrorIdentifier": "f3356bfb-9b1f-428f-9085-e0d0ed898a48", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 189, "timestamp": "2025-06-29 23:19:07", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 16 equipment maintenance tasks (15-day threshold)", "status": "Failed", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "error_code": 401, "error_response": {"ErrorIdentifier": "e00e2666-cd8c-4c91-bd9e-4785acdd59d8", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 190, "timestamp": "2025-06-29 23:19:07", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 30 equipment maintenance tasks (30-day threshold)", "status": "Failed", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "error_code": 401, "error_response": {"ErrorIdentifier": "8c26d6da-2570-461a-8945-a4270cfb89d9", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 191, "timestamp": "2025-06-29 23:20:57", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 4 equipment maintenance tasks (1-day threshold)", "status": "Failed", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "error_code": 401, "error_response": {"ErrorIdentifier": "560daaa5-bf19-4fc6-8f32-1acd240eb1bc", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 192, "timestamp": "2025-06-29 23:20:58", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 12 equipment maintenance tasks (7-day threshold)", "status": "Failed", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "error_code": 401, "error_response": {"ErrorIdentifier": "47d89644-d639-4129-ba42-1960773b5deb", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 193, "timestamp": "2025-06-29 23:20:58", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 16 equipment maintenance tasks (15-day threshold)", "status": "Failed", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "error_code": 401, "error_response": {"ErrorIdentifier": "c58a5fcd-a4fd-4fc9-9abc-8fbbfc851dc0", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 194, "timestamp": "2025-06-29 23:20:59", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Failed to send email reminder for 30 equipment maintenance tasks (30-day threshold)", "status": "Failed", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "error_code": 401, "error_response": {"ErrorIdentifier": "3b9f3405-b334-48ad-a359-c7213996d057", "StatusCode": 401, "ErrorMessage": "API key authentication/authorization failure. You may be unauthorized to access the API or your API key may be expired. Visit API keys management section to check your keys."}}}, {"id": 195, "timestamp": "2025-06-29 23:24:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 196, "timestamp": "2025-06-29 23:24:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 197, "timestamp": "2025-06-29 23:24:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 198, "timestamp": "2025-06-29 23:24:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 199, "timestamp": "2025-06-29 23:27:39", "event_type": "Equipment History Added", "performed_by": "admin", "description": "Added history note to OCM equipment 34234", "status": "Success", "details": {"equipment_id": "34234", "equipment_type": "ocm", "note_id": "f12423f1-9294-4190-baab-6aea86ddb2c5", "note_length": 17}}, {"id": 200, "timestamp": "2025-06-29 23:27:39", "event_type": "History Attachment Added", "performed_by": "admin", "description": "Added attachment 'incident_related_medical_device.pdf' to history note", "status": "Success", "details": {"note_id": "f12423f1-9294-4190-baab-6aea86ddb2c5", "attachment_id": "9c2cad76-ed70-4e7f-a249-5f78c0db8a6f", "filename": "incident_related_medical_device.pdf", "file_size": 2708815}}, {"id": 201, "timestamp": "2025-06-29 23:27:39", "event_type": "Equipment History Added", "performed_by": "admin", "description": "History Added OCM equipment: 34234", "status": "Success", "details": {"equipment_type": "OCM", "serial": "34234", "action": "History Added"}}, {"id": 202, "timestamp": "2025-06-29 23:29:13", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 203, "timestamp": "2025-06-30 00:11:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 204, "timestamp": "2025-06-30 00:11:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 205, "timestamp": "2025-06-30 00:11:07", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 206, "timestamp": "2025-06-30 00:11:08", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 207, "timestamp": "2025-06-30 02:44:28", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 208, "timestamp": "2025-06-30 02:44:49", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 209, "timestamp": "2025-06-30 02:44:50", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 210, "timestamp": "2025-06-30 03:12:58", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 211, "timestamp": "2025-06-30 03:12:59", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 212, "timestamp": "2025-06-30 03:12:59", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 213, "timestamp": "2025-06-30 03:13:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 214, "timestamp": "2025-06-30 03:27:28", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250628_143523.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250628_143523.json"}}, {"id": 215, "timestamp": "2025-06-30 03:27:38", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 216, "timestamp": "2025-06-30 03:27:38", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 217, "timestamp": "2025-06-30 03:28:19", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 218, "timestamp": "2025-06-30 03:28:19", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 219, "timestamp": "2025-06-30 03:28:24", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250628_143523.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250628_143523.json"}}, {"id": 220, "timestamp": "2025-06-30 03:29:19", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 221, "timestamp": "2025-06-30 03:29:20", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 222, "timestamp": "2025-06-30 03:29:20", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 223, "timestamp": "2025-06-30 03:29:21", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 224, "timestamp": "2025-06-30 03:49:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 225, "timestamp": "2025-06-30 03:49:22", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 226, "timestamp": "2025-06-30 03:49:22", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 227, "timestamp": "2025-06-30 03:49:22", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 228, "timestamp": "2025-06-30 03:49:23", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 229, "timestamp": "2025-06-30 03:49:23", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 230, "timestamp": "2025-06-30 03:58:47", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 231, "timestamp": "2025-06-30 03:59:09", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 232, "timestamp": "2025-06-30 03:59:10", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 233, "timestamp": "2025-06-30 04:00:13", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 234, "timestamp": "2025-06-30 04:00:14", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 235, "timestamp": "2025-06-30 04:00:15", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 236, "timestamp": "2025-06-30 04:28:16", "event_type": "Test Email", "performed_by": "User", "description": "Test email sent <NAME_EMAIL>", "status": "Success", "details": {"recipient": "<EMAIL>", "cc_emails": "<EMAIL>, <EMAIL>", "sender": "<EMAIL>"}}, {"id": 237, "timestamp": "2025-06-30 04:28:27", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 238, "timestamp": "2025-06-30 04:28:28", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 239, "timestamp": "2025-06-30 04:28:28", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 240, "timestamp": "2025-06-30 04:40:02", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250630_044001.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_044001.zip", "size_mb": 0.16, "files_backed_up": 22, "compression_level": 9}}, {"id": 241, "timestamp": "2025-06-30 04:40:09", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_044009.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044009.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 242, "timestamp": "2025-06-30 04:41:52", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044009.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044009.json"}}, {"id": 243, "timestamp": "2025-06-30 04:41:56", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250630_044001.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_044001.zip"}}, {"id": 244, "timestamp": "2025-06-30 04:41:56", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250630_044001.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_044001.zip"}}, {"id": 245, "timestamp": "2025-06-30 04:44:05", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044009.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044009.json"}}, {"id": 246, "timestamp": "2025-06-30 04:44:21", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 247, "timestamp": "2025-06-30 04:44:22", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 248, "timestamp": "2025-06-30 04:44:23", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 249, "timestamp": "2025-06-30 04:44:52", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 250, "timestamp": "2025-06-30 04:47:50", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 251, "timestamp": "2025-06-30 04:47:55", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044009.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044009.json"}}, {"id": 252, "timestamp": "2025-06-30 04:48:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 253, "timestamp": "2025-06-30 04:48:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 254, "timestamp": "2025-06-30 04:48:07", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 255, "timestamp": "2025-06-30 04:48:25", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 256, "timestamp": "2025-06-30 04:48:25", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 257, "timestamp": "2025-06-30 04:48:27", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 258, "timestamp": "2025-06-30 04:48:28", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 259, "timestamp": "2025-06-30 04:50:19", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 260, "timestamp": "2025-06-30 04:50:29", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 261, "timestamp": "2025-06-30 04:52:18", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 262, "timestamp": "2025-06-30 04:52:24", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044009.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044009.json"}}, {"id": 263, "timestamp": "2025-06-30 04:52:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 264, "timestamp": "2025-06-30 04:52:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 265, "timestamp": "2025-06-30 04:52:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 266, "timestamp": "2025-06-30 04:52:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 267, "timestamp": "2025-06-30 04:52:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 268, "timestamp": "2025-06-30 04:52:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 269, "timestamp": "2025-06-30 04:52:35", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 270, "timestamp": "2025-06-30 04:52:35", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 271, "timestamp": "2025-06-30 04:52:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 272, "timestamp": "2025-06-30 04:53:00", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_045300.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045300.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 273, "timestamp": "2025-06-30 04:53:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 274, "timestamp": "2025-06-30 04:53:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 275, "timestamp": "2025-06-30 04:53:08", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_045300.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045300.json"}}, {"id": 276, "timestamp": "2025-06-30 04:55:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 277, "timestamp": "2025-06-30 04:55:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 278, "timestamp": "2025-06-30 04:55:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 279, "timestamp": "2025-06-30 04:55:40", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 280, "timestamp": "2025-06-30 04:56:01", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_045300.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045300.json"}}, {"id": 281, "timestamp": "2025-06-30 04:56:03", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 282, "timestamp": "2025-06-30 04:56:12", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_045300.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045300.json"}}, {"id": 283, "timestamp": "2025-06-30 04:56:16", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_045616.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045616.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 284, "timestamp": "2025-06-30 04:56:17", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_044452.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044452.json"}}, {"id": 285, "timestamp": "2025-06-30 04:56:21", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_045616.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045616.json"}}, {"id": 286, "timestamp": "2025-06-30 04:56:26", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_044009.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_044009.json"}}, {"id": 287, "timestamp": "2025-06-30 04:56:29", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250630_044001.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_044001.zip"}}, {"id": 288, "timestamp": "2025-06-30 04:57:23", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250630_045723.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_045723.zip", "size_mb": 0.16, "files_backed_up": 22, "compression_level": 9}}, {"id": 289, "timestamp": "2025-06-30 04:57:26", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_045726.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045726.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 290, "timestamp": "2025-06-30 04:57:49", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_045749.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045749.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 291, "timestamp": "2025-06-30 05:00:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 292, "timestamp": "2025-06-30 05:00:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 293, "timestamp": "2025-06-30 05:01:27", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_045749.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045749.json"}}, {"id": 294, "timestamp": "2025-06-30 05:01:29", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_045726.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045726.json"}}, {"id": 295, "timestamp": "2025-06-30 05:03:10", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_045749.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045749.json"}}, {"id": 296, "timestamp": "2025-06-30 05:03:12", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250630_045726.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045726.json"}}, {"id": 297, "timestamp": "2025-06-30 05:03:14", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250630_045723.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_045723.zip"}}, {"id": 298, "timestamp": "2025-06-30 05:03:14", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250630_045723.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_045723.zip"}}, {"id": 299, "timestamp": "2025-06-30 05:03:17", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: settings_backup_20250628_143523.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250628_143523.json"}}, {"id": 300, "timestamp": "2025-06-30 05:03:19", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 301, "timestamp": "2025-06-30 05:03:19", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 302, "timestamp": "2025-06-30 05:03:26", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_045749.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045749.json"}}, {"id": 303, "timestamp": "2025-06-30 05:03:31", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_045726.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_045726.json"}}, {"id": 304, "timestamp": "2025-06-30 05:03:34", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250630_045723.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250630_045723.zip"}}, {"id": 305, "timestamp": "2025-06-30 05:32:42", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 8}}, {"id": 306, "timestamp": "2025-06-30 05:34:12", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 8}}, {"id": 307, "timestamp": "2025-06-30 05:34:34", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_053434.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053434.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 308, "timestamp": "2025-06-30 05:35:26", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 8}}, {"id": 309, "timestamp": "2025-06-30 05:35:48", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_053548.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053548.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 310, "timestamp": "2025-06-30 05:35:50", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file via API: settings_backup_20250630_053548.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053548.json"}}, {"id": 311, "timestamp": "2025-06-30 05:36:29", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 8}}, {"id": 312, "timestamp": "2025-06-30 05:36:50", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_053650.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053650.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 313, "timestamp": "2025-06-30 05:36:53", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file via API: settings_backup_20250630_053650.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053650.json"}}, {"id": 314, "timestamp": "2025-06-30 07:32:37", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 315, "timestamp": "2025-06-30 07:32:37", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 316, "timestamp": "2025-06-30 07:32:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 317, "timestamp": "2025-06-30 07:32:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 318, "timestamp": "2025-06-30 12:13:15", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 8}}, {"id": 319, "timestamp": "2025-06-30 12:13:37", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250630_121337.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_121337.json", "size_kb": 2.95, "settings_count": 17}}, {"id": 320, "timestamp": "2025-06-30 12:13:39", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file via API: settings_backup_20250630_121337.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_121337.json"}}, {"id": 321, "timestamp": "2025-07-02 01:45:13", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 322, "timestamp": "2025-07-02 01:45:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 323, "timestamp": "2025-07-02 01:45:15", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 324, "timestamp": "2025-07-02 02:19:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 325, "timestamp": "2025-07-02 02:19:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 326, "timestamp": "2025-07-02 02:19:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 327, "timestamp": "2025-07-02 02:19:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 328, "timestamp": "2025-07-02 03:25:41", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_121337.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_121337.json"}}, {"id": 329, "timestamp": "2025-07-02 03:25:47", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_053650.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053650.json"}}, {"id": 330, "timestamp": "2025-07-02 03:25:55", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_053548.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053548.json"}}, {"id": 331, "timestamp": "2025-07-02 03:27:30", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 332, "timestamp": "2025-07-02 03:27:31", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 333, "timestamp": "2025-07-02 03:27:31", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 334, "timestamp": "2025-07-02 03:27:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 335, "timestamp": "2025-07-02 03:29:00", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250630_053434.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250630_053434.json"}}, {"id": 336, "timestamp": "2025-07-02 03:29:05", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250628_143523.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250628_143523.json"}}, {"id": 337, "timestamp": "2025-07-02 03:29:11", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250628_143520.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250628_143520.zip"}}, {"id": 338, "timestamp": "2025-07-02 03:31:11", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 339, "timestamp": "2025-07-02 03:31:12", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 340, "timestamp": "2025-07-02 03:31:12", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 341, "timestamp": "2025-07-02 03:37:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 342, "timestamp": "2025-07-02 03:37:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 343, "timestamp": "2025-07-02 03:37:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 344, "timestamp": "2025-07-02 03:37:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 345, "timestamp": "2025-07-02 03:37:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 346, "timestamp": "2025-07-02 03:37:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 348, "timestamp": "2025-07-02 03:37:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 349, "timestamp": "2025-07-02 03:39:34", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 350, "timestamp": "2025-07-02 03:39:35", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 351, "timestamp": "2025-07-02 03:39:36", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 352, "timestamp": "2025-07-02 03:49:46", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 24}}, {"id": 353, "timestamp": "2025-07-02 05:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 354, "timestamp": "2025-07-02 05:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 4 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 4, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 355, "timestamp": "2025-07-02 05:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 356, "timestamp": "2025-07-02 05:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 12 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 12, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 357, "timestamp": "2025-07-02 05:29:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 358, "timestamp": "2025-07-02 05:29:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 16 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 16, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 359, "timestamp": "2025-07-02 05:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 360, "timestamp": "2025-07-02 05:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 361, "timestamp": "2025-07-02 23:34:59", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 362, "timestamp": "2025-07-03 21:27:01", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 363, "timestamp": "2025-07-03 21:27:31", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 364, "timestamp": "2025-07-03 21:27:32", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 37 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 37, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 365, "timestamp": "2025-07-03 21:27:33", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 113 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 113, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 366, "timestamp": "2025-07-03 21:27:34", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 105 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 105, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 367, "timestamp": "2025-07-03 21:35:04", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 368, "timestamp": "2025-07-03 21:49:23", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 369, "timestamp": "2025-07-03 21:55:03", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": false, "automatic_backup_interval_hours": 1}}, {"id": 370, "timestamp": "2025-07-03 23:28:27", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 371, "timestamp": "2025-07-03 23:28:28", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 37 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 37, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 372, "timestamp": "2025-07-03 23:28:29", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 113 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 113, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 373, "timestamp": "2025-07-03 23:28:30", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 105 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 105, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 374, "timestamp": "2025-07-03 23:31:46", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 2 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 2, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 375, "timestamp": "2025-07-03 23:31:47", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 37 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 37, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 376, "timestamp": "2025-07-03 23:31:48", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 113 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 113, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 377, "timestamp": "2025-07-03 23:31:49", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 105 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 105, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 378, "timestamp": "2025-07-04 02:38:22", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 1}}, {"id": 379, "timestamp": "2025-07-04 04:14:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 9 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 9, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 380, "timestamp": "2025-07-04 04:14:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 38 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 38, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 381, "timestamp": "2025-07-04 04:14:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 127 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 127, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 382, "timestamp": "2025-07-04 04:14:04", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 86 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 86, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 383, "timestamp": "2025-07-04 05:59:17", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_055916.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_055916.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 384, "timestamp": "2025-07-04 05:59:17", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_055917.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_055917.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 385, "timestamp": "2025-07-04 05:59:17", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 0.016666666666666666h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_055917.zip", "size_mb": 0.23, "interval_hours": 0.016666666666666666, "automatic": true}}, {"id": 386, "timestamp": "2025-07-04 06:00:17", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_060017.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060017.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 387, "timestamp": "2025-07-04 06:00:17", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 0.016666666666666666h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060017.zip", "size_mb": 0.23, "interval_hours": 0.016666666666666666, "automatic": true}}, {"id": 388, "timestamp": "2025-07-04 06:05:17", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_060517.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060517.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 389, "timestamp": "2025-07-04 06:05:17", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060517.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 390, "timestamp": "2025-07-04 06:08:40", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_055916.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_055916.zip"}}, {"id": 391, "timestamp": "2025-07-04 06:08:49", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_055917.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_055917.zip"}}, {"id": 392, "timestamp": "2025-07-04 06:08:54", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_060017.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060017.zip"}}, {"id": 393, "timestamp": "2025-07-04 06:09:05", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_060517.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060517.zip"}}, {"id": 394, "timestamp": "2025-07-04 06:09:11", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_060601.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_060601.zip"}}, {"id": 395, "timestamp": "2025-07-04 07:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 9 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 9, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 396, "timestamp": "2025-07-04 07:29:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 38 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 38, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 397, "timestamp": "2025-07-04 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 127 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 127, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 398, "timestamp": "2025-07-04 07:29:04", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 86 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 86, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 399, "timestamp": "2025-07-04 13:52:31", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 1}}, {"id": 400, "timestamp": "2025-07-04 13:59:34", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_135933.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135933.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 401, "timestamp": "2025-07-04 13:59:34", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_135933.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135933.zip"}}, {"id": 402, "timestamp": "2025-07-04 13:59:35", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_135934.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135934.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 403, "timestamp": "2025-07-04 13:59:35", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_135935.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135935.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 404, "timestamp": "2025-07-04 13:59:36", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_135935.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135935.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 405, "timestamp": "2025-07-04 13:59:36", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_135934.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135934.zip"}}, {"id": 406, "timestamp": "2025-07-04 13:59:36", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_135935.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_135935.zip"}}, {"id": 407, "timestamp": "2025-07-04 14:01:06", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140105_719172.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140105_719172.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 408, "timestamp": "2025-07-04 14:01:06", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140105_719172.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140105_719172.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140105_719172.zip"}}, {"id": 409, "timestamp": "2025-07-04 14:01:06", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140106_172112.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140106_172112.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 410, "timestamp": "2025-07-04 14:01:07", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140106_596442.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140106_596442.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 411, "timestamp": "2025-07-04 14:01:07", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140107_301642.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140107_301642.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 412, "timestamp": "2025-07-04 14:01:07", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140106_172112.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140106_172112.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140106_172112.zip"}}, {"id": 413, "timestamp": "2025-07-04 14:01:07", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140106_596442.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140106_596442.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140106_596442.zip"}}, {"id": 414, "timestamp": "2025-07-04 14:01:07", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140107_301642.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140107_301642.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140107_301642.zip"}}, {"id": 415, "timestamp": "2025-07-04 14:02:05", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140204_774146.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140204_774146.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 416, "timestamp": "2025-07-04 14:02:05", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140204_774146.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140204_774146.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140204_774146.zip"}}, {"id": 417, "timestamp": "2025-07-04 14:02:05", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140205_122404.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140205_122404.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 418, "timestamp": "2025-07-04 14:02:05", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140205_122404.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140205_122404.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140205_122404.zip"}}, {"id": 419, "timestamp": "2025-07-04 14:02:05", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140205_456604.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140205_456604.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 420, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140205_743405.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140205_743405.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 421, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140206_071546.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140206_071546.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 422, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140205_456604.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140205_456604.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140205_456604.zip"}}, {"id": 423, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140205_743405.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140205_743405.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140205_743405.zip"}}, {"id": 424, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140206_071546.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140206_071546.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140206_071546.zip"}}, {"id": 425, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140206_473393.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140206_473393.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 426, "timestamp": "2025-07-04 14:02:06", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140206_473393.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140206_473393.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140206_473393.zip"}}, {"id": 427, "timestamp": "2025-07-04 14:02:24", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140223.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140223.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 428, "timestamp": "2025-07-04 14:02:29", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_140229.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_140229.json", "size_kb": 2.86, "settings_count": 20}}, {"id": 429, "timestamp": "2025-07-04 14:02:34", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_140229.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_140229.json"}}, {"id": 430, "timestamp": "2025-07-04 14:02:40", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140223.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140223.zip"}}, {"id": 431, "timestamp": "2025-07-04 14:03:22", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140321_812585.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140321_812585.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 432, "timestamp": "2025-07-04 14:03:22", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140321_812585.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 433, "timestamp": "2025-07-04 14:03:22", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140321_972215.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140321_972215.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 434, "timestamp": "2025-07-04 14:03:22", "event_type": "Backup Deleted", "performed_by": "User", "description": "Failed to delete backup: ", "status": "Failed", "details": {"filename": "", "error": "[WinError 5] Access is denied: 'C:\\\\ALORFBIOMED\\\\data\\\\backups\\\\full\\\\'"}}, {"id": 435, "timestamp": "2025-07-04 14:05:32", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140321_972215.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140321_972215.zip"}}, {"id": 436, "timestamp": "2025-07-04 14:05:39", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140321_812585.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140321_812585.zip"}}, {"id": 437, "timestamp": "2025-07-04 14:06:10", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140609.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140609.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 438, "timestamp": "2025-07-04 14:06:15", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140609.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140609.zip"}}, {"id": 439, "timestamp": "2025-07-04 14:07:13", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140712_760985.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140712_760985.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 440, "timestamp": "2025-07-04 14:07:13", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140713_165364.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140713_165364.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 441, "timestamp": "2025-07-04 14:07:13", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140713_508717.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140713_508717.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 442, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140713_838793.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140713_838793.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 443, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140714_225300.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140714_225300.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 444, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140712_760985.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140712_760985.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140712_760985.zip"}}, {"id": 445, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140713_165364.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140713_165364.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140713_165364.zip"}}, {"id": 446, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140713_508717.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140713_508717.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140713_508717.zip"}}, {"id": 447, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140713_838793.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140713_838793.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140713_838793.zip"}}, {"id": 448, "timestamp": "2025-07-04 14:07:14", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140714_225300.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140714_225300.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_140714_225300.zip"}}, {"id": 449, "timestamp": "2025-07-04 14:08:30", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140830.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140830.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 450, "timestamp": "2025-07-04 14:08:35", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140830.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140830.zip"}}, {"id": 451, "timestamp": "2025-07-04 14:08:41", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_140841.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_140841.json", "size_kb": 2.86, "settings_count": 20}}, {"id": 452, "timestamp": "2025-07-04 14:08:46", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_140841.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_140841.json"}}, {"id": 453, "timestamp": "2025-07-04 14:08:52", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_140851.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140851.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 454, "timestamp": "2025-07-04 14:08:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_140851.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_140851.zip"}}, {"id": 455, "timestamp": "2025-07-04 14:13:13", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_141312.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_141312.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 456, "timestamp": "2025-07-04 14:13:15", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_141315.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_141315.json", "size_kb": 2.86, "settings_count": 20}}, {"id": 457, "timestamp": "2025-07-04 14:13:24", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_141315.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_141315.json"}}, {"id": 458, "timestamp": "2025-07-04 14:14:01", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_141312.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_141312.zip"}}, {"id": 459, "timestamp": "2025-07-04 14:16:55", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_141655_445956.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_141655_445956.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 460, "timestamp": "2025-07-04 14:16:55", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_141655_445956.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_141655_445956.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_141655_445956.zip"}}, {"id": 461, "timestamp": "2025-07-04 14:16:56", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_141655_803870.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_141655_803870.json", "size_kb": 2.86, "settings_count": 20}}, {"id": 462, "timestamp": "2025-07-04 14:16:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_141655_803870.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_141655_803870.json", "file_path": "C:\\ALORFBIOMED\\data\\backups\\settings\\settings_backup_20250704_141655_803870.json"}}, {"id": 463, "timestamp": "2025-07-04 14:16:56", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_141656_441399.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_141656_441399.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 464, "timestamp": "2025-07-04 14:16:56", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_141656_752549.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_141656_752549.json", "size_kb": 2.86, "settings_count": 20}}, {"id": 465, "timestamp": "2025-07-04 14:16:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_141656_441399.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_141656_441399.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_141656_441399.zip"}}, {"id": 466, "timestamp": "2025-07-04 14:16:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_141656_752549.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_141656_752549.json", "file_path": "C:\\ALORFBIOMED\\data\\backups\\settings\\settings_backup_20250704_141656_752549.json"}}, {"id": 467, "timestamp": "2025-07-04 14:21:45", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_142145_414836.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142145_414836.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 468, "timestamp": "2025-07-04 14:21:45", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_142145_414836.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142145_414836.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_142145_414836.zip"}}, {"id": 469, "timestamp": "2025-07-04 14:21:46", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_142145_695662.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_142145_695662.json", "size_kb": 2.86, "settings_count": 20}}, {"id": 470, "timestamp": "2025-07-04 14:21:46", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_142145_695662.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_142145_695662.json", "file_path": "C:\\ALORFBIOMED\\data\\backups\\settings\\settings_backup_20250704_142145_695662.json"}}, {"id": 471, "timestamp": "2025-07-04 14:22:54", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_142254_522205.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142254_522205.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 472, "timestamp": "2025-07-04 14:22:55", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_142255_096932.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142255_096932.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 473, "timestamp": "2025-07-04 14:22:56", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_142255_778587.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142255_778587.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 474, "timestamp": "2025-07-04 14:22:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_142254_522205.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142254_522205.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_142254_522205.zip"}}, {"id": 475, "timestamp": "2025-07-04 14:22:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_142255_096932.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142255_096932.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_142255_096932.zip"}}, {"id": 476, "timestamp": "2025-07-04 14:22:56", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_142255_778587.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142255_778587.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250704_142255_778587.zip"}}, {"id": 477, "timestamp": "2025-07-04 14:22:57", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_142256_593208.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_142256_593208.json", "size_kb": 2.84, "settings_count": 20}}, {"id": 478, "timestamp": "2025-07-04 14:22:57", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_142256_593208.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_142256_593208.json", "file_path": "C:\\ALORFBIOMED\\data\\backups\\settings\\settings_backup_20250704_142256_593208.json"}}, {"id": 479, "timestamp": "2025-07-04 14:23:43", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_142343.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142343.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 480, "timestamp": "2025-07-04 14:23:46", "event_type": "Backup Created", "performed_by": "System", "description": "Settings backup created: settings_backup_20250704_142346.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_142346.json", "size_kb": 2.84, "settings_count": 20}}, {"id": 481, "timestamp": "2025-07-04 14:23:51", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: settings_backup_20250704_142346.json", "status": "Success", "details": {"backup_type": "settings", "filename": "settings_backup_20250704_142346.json"}}, {"id": 482, "timestamp": "2025-07-04 14:23:57", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_142343.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_142343.zip"}}, {"id": 483, "timestamp": "2025-07-04 15:04:51", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_150449_945658.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_150449_945658.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 484, "timestamp": "2025-07-04 15:04:51", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_150449_945658.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 485, "timestamp": "2025-07-04 15:16:53", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_151652_857168.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_151652_857168.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 486, "timestamp": "2025-07-04 15:16:53", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_151652_857168.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 487, "timestamp": "2025-07-04 15:39:10", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250704_153909_976135.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_153909_976135.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 488, "timestamp": "2025-07-04 15:39:10", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_153909_976135.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 489, "timestamp": "2025-07-05 07:29:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 18 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 18, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 490, "timestamp": "2025-07-05 07:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 45 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 45, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 491, "timestamp": "2025-07-05 07:29:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 135 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 135, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 492, "timestamp": "2025-07-05 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 84 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 84, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 493, "timestamp": "2025-07-07 07:29:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 14 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 14, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 494, "timestamp": "2025-07-07 07:29:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 93 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 93, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 495, "timestamp": "2025-07-07 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 87 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 87, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 496, "timestamp": "2025-07-07 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 70 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 70, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 497, "timestamp": "2025-07-08 04:36:09", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_043608_711939.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_043608_711939.zip", "size_mb": 0.23, "files_backed_up": 25, "compression_level": 9}}, {"id": 498, "timestamp": "2025-07-08 04:36:09", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_043608_711939.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 499, "timestamp": "2025-07-08 04:38:04", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_150449_945658.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_150449_945658.zip"}}, {"id": 500, "timestamp": "2025-07-08 04:38:08", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_151554_177344.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_151554_177344.zip"}}, {"id": 501, "timestamp": "2025-07-08 04:38:12", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_151652_857168.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_151652_857168.zip"}}, {"id": 502, "timestamp": "2025-07-08 04:38:17", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_151856_022876.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_151856_022876.zip"}}, {"id": 503, "timestamp": "2025-07-08 04:38:22", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250704_153909_976135.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250704_153909_976135.zip"}}, {"id": 504, "timestamp": "2025-07-08 04:38:29", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_043608_711939.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_043608_711939.zip"}}, {"id": 505, "timestamp": "2025-07-08 04:42:08", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 19 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 19, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 506, "timestamp": "2025-07-08 04:42:10", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 88 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 88, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 507, "timestamp": "2025-07-08 04:42:11", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 89 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 89, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 508, "timestamp": "2025-07-08 04:42:12", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 68 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 68, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 509, "timestamp": "2025-07-08 07:29:00", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 19 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 19, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 510, "timestamp": "2025-07-08 07:29:01", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 88 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 88, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 511, "timestamp": "2025-07-08 07:29:02", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 89 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 89, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 512, "timestamp": "2025-07-08 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 68 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 68, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 513, "timestamp": "2025-07-08 20:06:38", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 19 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 19, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 514, "timestamp": "2025-07-08 20:06:39", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 88 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 88, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 515, "timestamp": "2025-07-08 20:06:40", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 89 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 89, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 516, "timestamp": "2025-07-08 20:06:41", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 68 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 68, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 517, "timestamp": "2025-07-08 20:07:07", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 19 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 19, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 518, "timestamp": "2025-07-08 20:07:08", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 88 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 88, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 519, "timestamp": "2025-07-08 20:07:09", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 89 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 89, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 520, "timestamp": "2025-07-08 20:07:10", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 68 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 68, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 521, "timestamp": "2025-07-08 22:45:51", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_224550_881116.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224550_881116.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 522, "timestamp": "2025-07-08 22:45:51", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224550_881116.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 523, "timestamp": "2025-07-08 22:48:32", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_224831_832895.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224831_832895.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 524, "timestamp": "2025-07-08 22:48:32", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224831_832895.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 525, "timestamp": "2025-07-08 22:49:17", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_224916_720132.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224916_720132.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 526, "timestamp": "2025-07-08 22:49:17", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224916_720132.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 527, "timestamp": "2025-07-08 22:49:54", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_224953_811579.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224953_811579.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 528, "timestamp": "2025-07-08 22:49:54", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224953_811579.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 529, "timestamp": "2025-07-08 23:02:49", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_230249_095789.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_230249_095789.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 530, "timestamp": "2025-07-08 23:02:49", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_230249_095789.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 531, "timestamp": "2025-07-08 23:45:52", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_234551_779168.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234551_779168.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 532, "timestamp": "2025-07-08 23:45:52", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234551_779168.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 533, "timestamp": "2025-07-08 23:48:32", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_234832_576367.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234832_576367.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 534, "timestamp": "2025-07-08 23:48:32", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234832_576367.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 535, "timestamp": "2025-07-08 23:49:17", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_234917_417777.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234917_417777.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 536, "timestamp": "2025-07-08 23:49:17", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234917_417777.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 537, "timestamp": "2025-07-08 23:49:55", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_234954_773369.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234954_773369.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 538, "timestamp": "2025-07-08 23:49:55", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234954_773369.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 539, "timestamp": "2025-07-08 23:59:47", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250708_235947_146870.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_235947_146870.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 540, "timestamp": "2025-07-08 23:59:47", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_235947_146870.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 541, "timestamp": "2025-07-09 00:02:50", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_000250_449834.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_000250_449834.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 542, "timestamp": "2025-07-09 00:02:51", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_000250_449834.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 543, "timestamp": "2025-07-09 00:45:52", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_004552_482003.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004552_482003.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 544, "timestamp": "2025-07-09 00:45:52", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004552_482003.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 545, "timestamp": "2025-07-09 00:48:33", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_004833_168250.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004833_168250.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 546, "timestamp": "2025-07-09 00:48:33", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004833_168250.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 547, "timestamp": "2025-07-09 00:48:36", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_004836_554542.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004836_554542.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 548, "timestamp": "2025-07-09 00:48:37", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004836_554542.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 549, "timestamp": "2025-07-09 00:49:18", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_004917_921946.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004917_921946.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 550, "timestamp": "2025-07-09 00:49:18", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004917_921946.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 551, "timestamp": "2025-07-09 00:49:55", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_004955_481919.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004955_481919.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 552, "timestamp": "2025-07-09 00:49:55", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004955_481919.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 553, "timestamp": "2025-07-09 00:59:48", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_005948_249685.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_005948_249685.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 554, "timestamp": "2025-07-09 00:59:48", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_005948_249685.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 555, "timestamp": "2025-07-09 01:02:56", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250709_010256_262469.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_010256_262469.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 556, "timestamp": "2025-07-09 01:02:57", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_010256_262469.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 557, "timestamp": "2025-07-10 03:46:43", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250710_034642_768648.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_034642_768648.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 558, "timestamp": "2025-07-10 03:46:43", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 1h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_034642_768648.zip", "size_mb": 0.23, "interval_hours": 1, "automatic": true}}, {"id": 559, "timestamp": "2025-07-10 04:00:50", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250710_034642_768648.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_034642_768648.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250710_034642_768648.zip"}}, {"id": 560, "timestamp": "2025-07-10 04:01:06", "event_type": "Setting Changed", "performed_by": "User", "description": "Backup settings updated via API", "status": "Success", "details": {"automatic_backup_enabled": true, "automatic_backup_interval_hours": 24}}, {"id": 561, "timestamp": "2025-07-10 04:01:31", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_010256_262469.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_010256_262469.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_010256_262469.zip"}}, {"id": 562, "timestamp": "2025-07-10 04:01:42", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_044934_516478.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_044934_516478.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_044934_516478.zip"}}, {"id": 563, "timestamp": "2025-07-10 04:01:47", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_103009_534314.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_103009_534314.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_103009_534314.zip"}}, {"id": 564, "timestamp": "2025-07-10 04:01:53", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_224427_650394.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224427_650394.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_224427_650394.zip"}}, {"id": 565, "timestamp": "2025-07-10 04:01:57", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_224550_881116.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224550_881116.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_224550_881116.zip"}}, {"id": 566, "timestamp": "2025-07-10 04:02:03", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_224831_832895.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224831_832895.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_224831_832895.zip"}}, {"id": 567, "timestamp": "2025-07-10 04:02:09", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_224916_720132.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224916_720132.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_224916_720132.zip"}}, {"id": 568, "timestamp": "2025-07-10 04:02:14", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_224953_811579.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_224953_811579.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_224953_811579.zip"}}, {"id": 569, "timestamp": "2025-07-10 04:02:19", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_230249_095789.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_230249_095789.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_230249_095789.zip"}}, {"id": 570, "timestamp": "2025-07-10 04:02:23", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_234551_779168.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234551_779168.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_234551_779168.zip"}}, {"id": 571, "timestamp": "2025-07-10 04:02:27", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_234832_576367.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234832_576367.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_234832_576367.zip"}}, {"id": 572, "timestamp": "2025-07-10 04:02:32", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_234917_417777.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234917_417777.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_234917_417777.zip"}}, {"id": 573, "timestamp": "2025-07-10 04:02:37", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_005948_249685.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_005948_249685.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_005948_249685.zip"}}, {"id": 574, "timestamp": "2025-07-10 04:02:42", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_234954_773369.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_234954_773369.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_234954_773369.zip"}}, {"id": 575, "timestamp": "2025-07-10 04:02:47", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_000250_449834.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_000250_449834.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_000250_449834.zip"}}, {"id": 576, "timestamp": "2025-07-10 04:02:57", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250709_004955_481919.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004955_481919.zip"}}, {"id": 577, "timestamp": "2025-07-10 04:02:57", "event_type": "Data Export", "performed_by": "User", "description": "Downloaded backup file: full_backup_20250709_004955_481919.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004955_481919.zip"}}, {"id": 578, "timestamp": "2025-07-10 04:02:59", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_004955_481919.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004955_481919.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_004955_481919.zip"}}, {"id": 579, "timestamp": "2025-07-10 04:03:06", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_004917_921946.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004917_921946.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_004917_921946.zip"}}, {"id": 580, "timestamp": "2025-07-10 04:03:11", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_004836_554542.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004836_554542.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_004836_554542.zip"}}, {"id": 581, "timestamp": "2025-07-10 04:03:16", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_004833_168250.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004833_168250.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_004833_168250.zip"}}, {"id": 582, "timestamp": "2025-07-10 04:03:21", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250709_004552_482003.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250709_004552_482003.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250709_004552_482003.zip"}}, {"id": 583, "timestamp": "2025-07-10 04:03:26", "event_type": "Backup Deleted", "performed_by": "User", "description": "Backup deleted: full_backup_20250708_235947_146870.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250708_235947_146870.zip", "file_path": "C:\\ALORFBIOMED\\data\\backups\\full\\full_backup_20250708_235947_146870.zip"}}, {"id": 584, "timestamp": "2025-07-10 04:09:41", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250710_040940_531377.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_040940_531377.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 585, "timestamp": "2025-07-10 04:09:41", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_040940_531377.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 586, "timestamp": "2025-07-10 04:09:43", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250710_040943_218221.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_040943_218221.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 587, "timestamp": "2025-07-10 04:09:44", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_040943_218221.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 588, "timestamp": "2025-07-10 04:13:12", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 589, "timestamp": "2025-07-10 04:46:45", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250710_044644_638890.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_044644_638890.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 590, "timestamp": "2025-07-10 04:46:45", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250710_044644_638890.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 591, "timestamp": "2025-07-19 19:21:19", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250719_192118_740775.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_192118_740775.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 592, "timestamp": "2025-07-19 19:21:19", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_192118_740775.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 593, "timestamp": "2025-07-19 19:21:23", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250719_192123_240591.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_192123_240591.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 594, "timestamp": "2025-07-19 19:21:23", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_192123_240591.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 595, "timestamp": "2025-07-19 19:30:26", "event_type": "User Action", "performed_by": "User", "description": "Accessed audit log page", "status": "Info", "details": {}}, {"id": 596, "timestamp": "2025-07-19 19:38:37", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250719_193836_759710.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_193836_759710.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 597, "timestamp": "2025-07-19 19:38:37", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_193836_759710.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 598, "timestamp": "2025-07-19 19:38:40", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250719_193839_770662.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_193839_770662.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 599, "timestamp": "2025-07-19 19:38:40", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250719_193839_770662.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 600, "timestamp": "2025-07-20 00:04:53", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_000452_991137.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_000452_991137.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 601, "timestamp": "2025-07-20 00:04:53", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_000452_991137.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 602, "timestamp": "2025-07-20 00:04:56", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_000455_905588.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_000455_905588.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 603, "timestamp": "2025-07-20 00:04:56", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_000455_905588.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 604, "timestamp": "2025-07-20 00:09:44", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_000944_279564.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_000944_279564.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 605, "timestamp": "2025-07-20 00:09:44", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_000944_279564.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 606, "timestamp": "2025-07-20 00:13:23", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_001323_035020.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_001323_035020.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 607, "timestamp": "2025-07-20 00:13:23", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_001323_035020.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 608, "timestamp": "2025-07-20 00:13:26", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_001326_301289.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_001326_301289.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 609, "timestamp": "2025-07-20 00:13:27", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_001326_301289.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 610, "timestamp": "2025-07-20 00:25:26", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_002526_086481.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_002526_086481.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 611, "timestamp": "2025-07-20 00:25:26", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_002526_086481.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 612, "timestamp": "2025-07-20 00:45:09", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_004508_652012.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_004508_652012.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 613, "timestamp": "2025-07-20 00:45:09", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_004508_652012.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 614, "timestamp": "2025-07-20 14:09:59", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_140958_591705.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_140958_591705.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 615, "timestamp": "2025-07-20 14:09:59", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_140958_591705.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 616, "timestamp": "2025-07-20 14:10:01", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_141001_499121.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141001_499121.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 617, "timestamp": "2025-07-20 14:10:01", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141001_499121.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 618, "timestamp": "2025-07-20 14:11:15", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_141114_782247.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141114_782247.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 619, "timestamp": "2025-07-20 14:11:15", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141114_782247.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 620, "timestamp": "2025-07-20 14:13:50", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_141350_051504.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141350_051504.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 621, "timestamp": "2025-07-20 14:13:50", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141350_051504.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 622, "timestamp": "2025-07-20 14:16:15", "event_type": "Backup Created", "performed_by": "System", "description": "Full application backup created: full_backup_20250720_141614_815089.zip", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141614_815089.zip", "size_mb": 0.23, "files_backed_up": 28, "compression_level": 9}}, {"id": 623, "timestamp": "2025-07-20 14:16:15", "event_type": "Backup Created", "performed_by": "System", "description": "Automatic full backup created successfully (interval: 24h)", "status": "Success", "details": {"backup_type": "full", "filename": "full_backup_20250720_141614_815089.zip", "size_mb": 0.23, "interval_hours": 24, "automatic": true}}, {"id": 624, "timestamp": "2025-07-20 14:36:15", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 34 equipment maintenance tasks (1-day threshold, URGENT priority)", "status": "Success", "details": {"threshold_days": 1, "priority_level": "URGENT", "equipment_count": 34, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 625, "timestamp": "2025-07-20 14:36:16", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 29 equipment maintenance tasks (7-day threshold, HIGH priority)", "status": "Success", "details": {"threshold_days": 7, "priority_level": "HIGH", "equipment_count": 29, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 626, "timestamp": "2025-07-20 14:36:17", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 47 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 47, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 627, "timestamp": "2025-07-20 14:36:18", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 136 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 136, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}]