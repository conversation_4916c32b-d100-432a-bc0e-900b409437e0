#!/usr/bin/env python3
"""
ALORF BIOMED Final Phase Migration Script
Migrates Settings, Audit Logs, and Push Subscriptions to Supabase
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration_final_phase.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalPhaseMigration:
    def __init__(self):
        """Initialize the migration with Supabase client."""
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables")
        
        self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.info("Supabase client initialized successfully")

    def execute_sql_file(self, sql_file_path: str) -> bool:
        """Execute SQL migration file."""
        try:
            logger.info(f"Executing SQL migration file: {sql_file_path}")
            
            with open(sql_file_path, 'r') as f:
                sql_content = f.read()
            
            # Split SQL content by statements (basic approach)
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for i, statement in enumerate(statements):
                if statement:
                    try:
                        logger.debug(f"Executing statement {i+1}/{len(statements)}")
                        # Use rpc to execute raw SQL
                        result = self.supabase.rpc('exec_sql', {'sql': statement}).execute()
                        logger.debug(f"Statement {i+1} executed successfully")
                    except Exception as e:
                        logger.warning(f"Statement {i+1} failed (may be expected): {e}")
                        # Continue with other statements
            
            logger.info("SQL migration file executed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to execute SQL file: {e}")
            return False

    def migrate_settings_data(self) -> bool:
        """Migrate settings from JSON to Supabase."""
        try:
            logger.info("Starting settings data migration...")
            
            # Load current settings
            settings_path = Path(Config.SETTINGS_JSON_PATH)
            if not settings_path.exists():
                logger.warning(f"Settings file not found: {settings_path}")
                return True  # Not an error if file doesn't exist
            
            with open(settings_path, 'r') as f:
                settings_data = json.load(f)
            
            logger.info(f"Loaded settings data with {len(settings_data)} keys")
            
            # Separate users and roles from other settings
            users_data = settings_data.pop('users', [])
            roles_data = settings_data.pop('roles', {})
            
            # Migrate users
            if users_data:
                logger.info(f"Migrating {len(users_data)} users...")
                for user in users_data:
                    user_record = {
                        'username': user['username'],
                        'password_hash': user['password'],
                        'role': user['role'],
                        'is_active': True
                    }
                    
                    # Insert or update user
                    result = self.supabase.table('users').upsert(user_record, on_conflict='username').execute()
                    logger.debug(f"Migrated user: {user['username']}")
            
            # Migrate roles (update existing ones with permissions)
            if roles_data:
                logger.info(f"Updating {len(roles_data)} roles...")
                for role_name, role_info in roles_data.items():
                    role_record = {
                        'name': role_name,
                        'permissions': role_info.get('permissions', []),
                        'description': f'{role_name} role permissions'
                    }
                    
                    result = self.supabase.table('roles').upsert(role_record, on_conflict='name').execute()
                    logger.debug(f"Updated role: {role_name}")
            
            # Migrate other settings as key-value pairs
            logger.info(f"Migrating {len(settings_data)} settings...")
            for key, value in settings_data.items():
                # Determine category based on key
                category = 'general'
                if 'email' in key.lower():
                    category = 'email'
                elif 'push' in key.lower() or 'notification' in key.lower():
                    category = 'notifications'
                elif 'backup' in key.lower():
                    category = 'backup'
                elif 'scheduler' in key.lower() or 'reminder' in key.lower():
                    category = 'scheduler'
                
                setting_record = {
                    'key': key,
                    'value': value,
                    'category': category,
                    'description': f'Migrated setting: {key}'
                }
                
                result = self.supabase.table('settings').upsert(setting_record, on_conflict='key').execute()
                logger.debug(f"Migrated setting: {key}")
            
            logger.info("Settings data migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate settings data: {e}")
            return False

    def migrate_audit_logs(self) -> bool:
        """Migrate audit logs from JSON to Supabase."""
        try:
            logger.info("Starting audit logs migration...")
            
            # Load audit logs
            audit_path = Path(Config.AUDIT_LOG_JSON_PATH)
            if not audit_path.exists():
                logger.warning(f"Audit log file not found: {audit_path}")
                return True  # Not an error if file doesn't exist
            
            with open(audit_path, 'r') as f:
                audit_logs = json.load(f)
            
            logger.info(f"Loaded {len(audit_logs)} audit log entries")
            
            # Migrate in batches to avoid memory issues
            batch_size = 100
            total_batches = (len(audit_logs) + batch_size - 1) // batch_size
            
            for i in range(0, len(audit_logs), batch_size):
                batch = audit_logs[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                logger.info(f"Migrating batch {batch_num}/{total_batches} ({len(batch)} entries)")
                
                # Prepare batch data
                batch_records = []
                for log_entry in batch:
                    # Convert timestamp to proper format
                    timestamp_str = log_entry.get('timestamp', '')
                    try:
                        # Parse the timestamp and convert to ISO format
                        dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        iso_timestamp = dt.isoformat()
                    except:
                        iso_timestamp = datetime.now().isoformat()
                    
                    record = {
                        'timestamp': iso_timestamp,
                        'event_type': log_entry.get('event_type', 'Unknown'),
                        'performed_by': log_entry.get('performed_by', 'System'),
                        'description': log_entry.get('description', ''),
                        'status': log_entry.get('status', 'Success'),
                        'details': log_entry.get('details', {})
                    }
                    batch_records.append(record)
                
                # Insert batch
                result = self.supabase.table('audit_logs').insert(batch_records).execute()
                logger.debug(f"Batch {batch_num} migrated successfully")
            
            logger.info("Audit logs migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate audit logs: {e}")
            return False

    def migrate_push_subscriptions(self) -> bool:
        """Migrate push subscriptions from JSON to Supabase."""
        try:
            logger.info("Starting push subscriptions migration...")
            
            # Load push subscriptions
            push_path = Path(Config.PUSH_SUBSCRIPTIONS_JSON_PATH)
            if not push_path.exists():
                logger.warning(f"Push subscriptions file not found: {push_path}")
                return True  # Not an error if file doesn't exist
            
            with open(push_path, 'r') as f:
                subscriptions = json.load(f)
            
            logger.info(f"Loaded {len(subscriptions)} push subscriptions")
            
            if not subscriptions:
                logger.info("No push subscriptions to migrate")
                return True
            
            # Migrate subscriptions
            for subscription in subscriptions:
                record = {
                    'endpoint': subscription.get('endpoint', ''),
                    'p256dh_key': subscription.get('keys', {}).get('p256dh', ''),
                    'auth_key': subscription.get('keys', {}).get('auth', ''),
                    'is_active': True
                }
                
                result = self.supabase.table('push_subscriptions').upsert(record, on_conflict='endpoint').execute()
                logger.debug(f"Migrated subscription: {subscription.get('endpoint', 'Unknown')[:50]}...")
            
            logger.info("Push subscriptions migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate push subscriptions: {e}")
            return False

    def verify_migration(self) -> bool:
        """Verify that all data was migrated successfully."""
        try:
            logger.info("Verifying migration...")
            
            # Check settings
            settings_result = self.supabase.table('settings').select('*').execute()
            settings_count = len(settings_result.data)
            logger.info(f"Settings table contains {settings_count} records")
            
            # Check users
            users_result = self.supabase.table('users').select('*').execute()
            users_count = len(users_result.data)
            logger.info(f"Users table contains {users_count} records")
            
            # Check roles
            roles_result = self.supabase.table('roles').select('*').execute()
            roles_count = len(roles_result.data)
            logger.info(f"Roles table contains {roles_count} records")
            
            # Check audit logs
            audit_result = self.supabase.table('audit_logs').select('*', count='exact').execute()
            audit_count = audit_result.count
            logger.info(f"Audit logs table contains {audit_count} records")
            
            # Check push subscriptions
            push_result = self.supabase.table('push_subscriptions').select('*').execute()
            push_count = len(push_result.data)
            logger.info(f"Push subscriptions table contains {push_count} records")
            
            logger.info("Migration verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to verify migration: {e}")
            return False

def main():
    """Main migration function."""
    logger.info("=== ALORF BIOMED Final Phase Migration Started ===")
    
    try:
        migration = FinalPhaseMigration()
        
        # Step 1: Execute SQL schema migration
        logger.info("Step 1: Creating database schemas...")
        if not migration.execute_sql_file('supabase_migration_final_phase.sql'):
            logger.error("Failed to create database schemas")
            return False
        
        # Step 2: Migrate settings data
        logger.info("Step 2: Migrating settings data...")
        if not migration.migrate_settings_data():
            logger.error("Failed to migrate settings data")
            return False
        
        # Step 3: Migrate audit logs
        logger.info("Step 3: Migrating audit logs...")
        if not migration.migrate_audit_logs():
            logger.error("Failed to migrate audit logs")
            return False
        
        # Step 4: Migrate push subscriptions
        logger.info("Step 4: Migrating push subscriptions...")
        if not migration.migrate_push_subscriptions():
            logger.error("Failed to migrate push subscriptions")
            return False
        
        # Step 5: Verify migration
        logger.info("Step 5: Verifying migration...")
        if not migration.verify_migration():
            logger.error("Migration verification failed")
            return False
        
        logger.info("=== ALORF BIOMED Final Phase Migration Completed Successfully ===")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
