#!/usr/bin/env python3
"""
Comprehensive verification script for all ALORF BIOMED modules.
This script will verify all database content is accessible through the web interface.
"""

import requests
import time
import sys

def verify_module(url, module_name, expected_records=None, search_text=None):
    """Verify a specific module is working and accessible."""
    try:
        print(f"🔍 Verifying {module_name}...")
        response = requests.get(url, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ {module_name}: HTTP {response.status_code}")
            return False
        
        content = response.text
        
        # Check for expected records
        if expected_records:
            if str(expected_records) in content and 'total records' in content:
                print(f"✅ {module_name}: {expected_records} records verified")
                return True
            else:
                print(f"❌ {module_name}: Expected {expected_records} records not found")
                return False
        
        # Check for search text
        if search_text:
            if search_text in content:
                print(f"✅ {module_name}: Content verified")
                return True
            else:
                print(f"❌ {module_name}: Expected content not found")
                return False
        
        # General success check
        if 'ALORF BIOMED SYSTEM' in content:
            print(f"✅ {module_name}: Page loaded successfully")
            return True
        else:
            print(f"❌ {module_name}: Page content invalid")
            return False
            
    except Exception as e:
        print(f"❌ {module_name}: Error - {e}")
        return False

def main():
    """Main verification function."""
    print("🎯 ALORF BIOMED COMPREHENSIVE MODULE VERIFICATION")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    results = {}
    
    # Test modules
    modules = [
        {
            'name': 'Dashboard',
            'url': f'{base_url}/',
            'expected_records': None,
            'search_text': 'Total Equipment'
        },
        {
            'name': 'PPM Equipment List',
            'url': f'{base_url}/equipment/ppm',
            'expected_records': 1000,
            'search_text': None
        },
        {
            'name': 'OCM Equipment List',
            'url': f'{base_url}/equipment/ocm',
            'expected_records': 610,
            'search_text': None
        },
        {
            'name': 'Training Module',
            'url': f'{base_url}/training',
            'expected_records': None,
            'search_text': 'Training Records'
        },
        {
            'name': 'Settings Page',
            'url': f'{base_url}/settings',
            'expected_records': None,
            'search_text': 'System Settings'
        },
        {
            'name': 'Audit Log',
            'url': f'{base_url}/audit-log',
            'expected_records': None,
            'search_text': 'Audit Log'
        }
    ]
    
    # Verify each module
    for module in modules:
        success = verify_module(
            module['url'], 
            module['name'], 
            module.get('expected_records'),
            module.get('search_text')
        )
        results[module['name']] = success
        time.sleep(1)  # Brief pause between requests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    total_modules = len(results)
    successful_modules = sum(results.values())
    
    for module_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {module_name}")
    
    print(f"\n🎯 OVERALL RESULT: {successful_modules}/{total_modules} modules verified")
    
    if successful_modules == total_modules:
        print("🎉 ALL MODULES VERIFIED SUCCESSFULLY!")
        print("✅ Database content is fully accessible through the web interface")
        print("✅ Supabase migration was successful")
        print("✅ All 6,131 records across 13 tables are working correctly")
    else:
        print("⚠️  Some modules failed verification")
        print("❌ Manual investigation required")
    
    return successful_modules == total_modules

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
