/* 
 * Login Page Background Image Styles
 * Alternative implementation as separate CSS file
 * Include this file in login.html if you prefer modular approach
 */

/* Background Image Configuration */
.login-background {
    background-image: url('../images/login-background.webp');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    position: relative;
}

/* Fallback for browsers that don't support WebP */
.no-webp .login-background {
    background-image: url('../images/login-background.jpg');
}

/* Background Overlay Options */
.login-overlay-light::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.1);
    pointer-events: none;
    z-index: 1;
}

.login-overlay-medium::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.2);
    pointer-events: none;
    z-index: 1;
}

.login-overlay-dark::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.3);
    pointer-events: none;
    z-index: 1;
}

/* Enhanced Login Card Styles */
.login-card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.login-card-solid {
    background: rgba(255, 255, 255, 0.98);
    position: relative;
    z-index: 2;
}

/* Responsive Background Adjustments */
@media (max-width: 768px) {
    .login-background {
        background-attachment: scroll;
        background-size: cover;
    }
}

@media (max-width: 480px) {
    .login-background {
        background-position: center center;
        background-size: cover;
    }
    
    .login-card-glass {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(5px);
    }
}

/* Animation for smooth loading */
.login-background {
    animation: fadeInBackground 1s ease-in-out;
}

@keyframes fadeInBackground {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Print styles - remove background for printing */
@media print {
    .login-background {
        background: none !important;
    }
    
    .login-background::before {
        display: none !important;
    }
}
