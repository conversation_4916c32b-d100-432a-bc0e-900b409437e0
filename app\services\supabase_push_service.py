"""
Supabase-based Push Subscription Service
Replaces JSON file-based push subscriptions with Supabase database storage
"""

import logging
from typing import List, Dict, Any, Optional
from supabase import create_client, Client

from app.config import Config

logger = logging.getLogger(__name__)

class SupabasePushService:
    """Service for managing push subscriptions using Supabase database."""
    
    def __init__(self):
        """Initialize the Supabase client."""
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.debug("SupabasePushService initialized")

    def add_subscription(self, subscription_data: Dict[str, Any]) -> bool:
        """
        Add a new push subscription to the database.
        
        Args:
            subscription_data: Dictionary containing subscription info with keys:
                - endpoint: Push service endpoint URL
                - keys: Dictionary with p256dh and auth keys
                
        Returns:
            bool: True if added successfully, False otherwise
        """
        try:
            # Extract keys from subscription data
            keys = subscription_data.get('keys', {})
            
            record = {
                'endpoint': subscription_data.get('endpoint', ''),
                'p256dh_key': keys.get('p256dh', ''),
                'auth_key': keys.get('auth', ''),
                'is_active': True
            }
            
            # Use insert instead of upsert to avoid constraint issues
            result = self.supabase.table('push_subscriptions').insert(record).execute()
            
            logger.info(f"Push subscription added/updated: {subscription_data.get('endpoint', 'Unknown')[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add push subscription: {e}")
            return False

    def remove_subscription(self, endpoint: str) -> bool:
        """
        Remove a push subscription from the database.
        
        Args:
            endpoint: The endpoint URL of the subscription to remove
            
        Returns:
            bool: True if removed successfully, False otherwise
        """
        try:
            result = self.supabase.table('push_subscriptions').delete().eq('endpoint', endpoint).execute()
            
            logger.info(f"Push subscription removed: {endpoint[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove push subscription: {e}")
            return False

    def get_all_subscriptions(self) -> List[Dict[str, Any]]:
        """
        Get all active push subscriptions from the database.
        
        Returns:
            List of subscription dictionaries in the format expected by webpush
        """
        try:
            result = self.supabase.table('push_subscriptions').select('*').eq('is_active', True).execute()
            
            subscriptions = []
            for sub in result.data:
                # Convert to format expected by webpush library
                subscription = {
                    'endpoint': sub['endpoint'],
                    'keys': {
                        'p256dh': sub['p256dh_key'],
                        'auth': sub['auth_key']
                    }
                }
                subscriptions.append(subscription)
            
            logger.info(f"Retrieved {len(subscriptions)} active push subscriptions")
            return subscriptions
            
        except Exception as e:
            logger.error(f"Failed to get push subscriptions: {e}")
            return []

    def get_subscription_by_endpoint(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific push subscription by endpoint.
        
        Args:
            endpoint: The endpoint URL to search for
            
        Returns:
            Subscription dictionary or None if not found
        """
        try:
            result = self.supabase.table('push_subscriptions').select('*').eq('endpoint', endpoint).execute()
            
            if result.data:
                sub = result.data[0]
                subscription = {
                    'endpoint': sub['endpoint'],
                    'keys': {
                        'p256dh': sub['p256dh_key'],
                        'auth': sub['auth_key']
                    }
                }
                return subscription
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to get push subscription by endpoint: {e}")
            return None

    def deactivate_subscription(self, endpoint: str) -> bool:
        """
        Deactivate a push subscription (mark as inactive instead of deleting).
        
        Args:
            endpoint: The endpoint URL of the subscription to deactivate
            
        Returns:
            bool: True if deactivated successfully, False otherwise
        """
        try:
            result = self.supabase.table('push_subscriptions').update({'is_active': False}).eq('endpoint', endpoint).execute()
            
            logger.info(f"Push subscription deactivated: {endpoint[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to deactivate push subscription: {e}")
            return False

    def reactivate_subscription(self, endpoint: str) -> bool:
        """
        Reactivate a push subscription.
        
        Args:
            endpoint: The endpoint URL of the subscription to reactivate
            
        Returns:
            bool: True if reactivated successfully, False otherwise
        """
        try:
            result = self.supabase.table('push_subscriptions').update({'is_active': True}).eq('endpoint', endpoint).execute()
            
            logger.info(f"Push subscription reactivated: {endpoint[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reactivate push subscription: {e}")
            return False

    def update_last_used(self, endpoint: str) -> bool:
        """
        Update the last_used_at timestamp for a subscription.
        
        Args:
            endpoint: The endpoint URL of the subscription
            
        Returns:
            bool: True if updated successfully, False otherwise
        """
        try:
            from datetime import datetime
            
            result = self.supabase.table('push_subscriptions').update({
                'last_used_at': datetime.now().isoformat()
            }).eq('endpoint', endpoint).execute()
            
            logger.debug(f"Updated last_used_at for subscription: {endpoint[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update last_used_at: {e}")
            return False

    def cleanup_inactive_subscriptions(self, days_inactive: int = 30) -> int:
        """
        Remove subscriptions that haven't been used for a specified number of days.
        
        Args:
            days_inactive: Number of days of inactivity before removal
            
        Returns:
            Number of subscriptions removed
        """
        try:
            from datetime import datetime, timedelta
            
            cutoff_date = (datetime.now() - timedelta(days=days_inactive)).isoformat()
            
            # First, get the count of subscriptions to be removed
            count_result = self.supabase.table('push_subscriptions').select('*', count='exact').lt('last_used_at', cutoff_date).execute()
            count = count_result.count
            
            # Remove inactive subscriptions
            result = self.supabase.table('push_subscriptions').delete().lt('last_used_at', cutoff_date).execute()
            
            logger.info(f"Cleaned up {count} inactive push subscriptions (inactive for {days_inactive} days)")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup inactive subscriptions: {e}")
            return 0

    def get_subscription_stats(self) -> Dict[str, int]:
        """
        Get statistics about push subscriptions.
        
        Returns:
            Dictionary with subscription statistics
        """
        try:
            # Get total count
            total_result = self.supabase.table('push_subscriptions').select('*', count='exact').execute()
            total_count = total_result.count
            
            # Get active count
            active_result = self.supabase.table('push_subscriptions').select('*', count='exact').eq('is_active', True).execute()
            active_count = active_result.count
            
            # Get inactive count
            inactive_count = total_count - active_count
            
            stats = {
                'total_subscriptions': total_count,
                'active_subscriptions': active_count,
                'inactive_subscriptions': inactive_count
            }
            
            logger.info(f"Push subscription stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get subscription stats: {e}")
            return {'total_subscriptions': 0, 'active_subscriptions': 0, 'inactive_subscriptions': 0}

    def migrate_from_json(self, json_subscriptions: List[Dict[str, Any]]) -> bool:
        """
        Migrate push subscriptions from JSON format to Supabase.
        
        Args:
            json_subscriptions: List of subscription dictionaries from JSON file
            
        Returns:
            bool: True if migration successful, False otherwise
        """
        try:
            logger.info(f"Migrating {len(json_subscriptions)} push subscriptions from JSON to Supabase")
            
            if not json_subscriptions:
                logger.info("No push subscriptions to migrate")
                return True
            
            # Prepare subscription records
            subscription_records = []
            for subscription in json_subscriptions:
                keys = subscription.get('keys', {})
                record = {
                    'endpoint': subscription.get('endpoint', ''),
                    'p256dh_key': keys.get('p256dh', ''),
                    'auth_key': keys.get('auth', ''),
                    'is_active': True
                }
                subscription_records.append(record)
            
            # Insert all subscriptions
            result = self.supabase.table('push_subscriptions').insert(subscription_records).execute()
            
            logger.info("Push subscriptions migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate push subscriptions from JSON: {e}")
            return False


# Global instance for backward compatibility
_push_service = None

def get_push_service() -> SupabasePushService:
    """Get the global push service instance."""
    global _push_service
    if _push_service is None:
        _push_service = SupabasePushService()
    return _push_service
