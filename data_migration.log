2025-07-20 15:07:35,809 - INFO - === ALORF BIOMED Data Migration Started ===
2025-07-20 15:07:37,455 - INFO - Supabase client initialized
2025-07-20 15:07:37,455 - INFO - Step 1: Migrating audit logs...
2025-07-20 15:07:37,456 - INFO - Starting audit logs migration...
2025-07-20 15:07:37,459 - INFO - Loaded 625 audit log entries from data\audit_log.json
2025-07-20 15:07:37,842 - INFO - Migrating batch 1/13 (50 entries)
2025-07-20 15:07:38,109 - ERROR - Failed to migrate audit logs: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'description' column of 'audit_logs' in the schema cache"}
2025-07-20 15:07:38,109 - ERROR - Failed to migrate audit logs
2025-07-20 15:40:32,404 - INFO - === ALORF BIOMED Data Migration Started ===
2025-07-20 15:40:33,899 - INFO - Supabase client initialized
2025-07-20 15:40:33,908 - INFO - Step 1: Migrating audit logs...
2025-07-20 15:40:33,926 - INFO - Starting audit logs migration...
2025-07-20 15:40:33,937 - INFO - Loaded 627 audit log entries from data\audit_log.json
2025-07-20 15:40:34,803 - INFO - Migrating batch 1/13 (50 entries)
2025-07-20 15:40:35,231 - ERROR - Failed to migrate audit logs: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'event_type' column of 'audit_logs' in the schema cache"}
2025-07-20 15:40:35,264 - ERROR - Failed to migrate audit logs
