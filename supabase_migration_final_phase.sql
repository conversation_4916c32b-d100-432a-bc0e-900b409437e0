-- =====================================================
-- ALORF BIOMED Final Phase Supabase Migration
-- Migrating Settings, Audit Logs, and Push Subscriptions
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for settings
CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);
CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category);
CREATE INDEX IF NOT EXISTS idx_settings_updated_at ON settings(updated_at);

-- =====================================================
-- 2. AUDIT LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type VARCHAR(100) NOT NULL,
    performed_by VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'Success',
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit logs (optimized for common queries)
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_performed_by ON audit_logs(performed_by);
CREATE INDEX IF NOT EXISTS idx_audit_logs_status ON audit_logs(status);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at DESC);

-- Composite index for common filtering scenarios
CREATE INDEX IF NOT EXISTS idx_audit_logs_composite ON audit_logs(event_type, performed_by, timestamp DESC);

-- =====================================================
-- 3. PUSH SUBSCRIPTIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint TEXT UNIQUE NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    user_agent TEXT,
    ip_address INET,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for push subscriptions
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_endpoint ON push_subscriptions(endpoint);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_active ON push_subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_created_at ON push_subscriptions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_last_used ON push_subscriptions(last_used_at DESC);

-- =====================================================
-- 4. USERS TABLE (for user management from settings)
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(100) NOT NULL DEFAULT 'Viewer',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for users
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);

-- =====================================================
-- 5. ROLES TABLE (for role-based permissions)
-- =====================================================
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    permissions JSONB NOT NULL DEFAULT '[]',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for roles
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);

-- =====================================================
-- 6. TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_subscriptions_updated_at BEFORE UPDATE ON push_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 7. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

-- Create policies for service key access (full access for backend)
CREATE POLICY "Service key full access" ON settings FOR ALL USING (true);
CREATE POLICY "Service key full access" ON audit_logs FOR ALL USING (true);
CREATE POLICY "Service key full access" ON push_subscriptions FOR ALL USING (true);
CREATE POLICY "Service key full access" ON users FOR ALL USING (true);
CREATE POLICY "Service key full access" ON roles FOR ALL USING (true);

-- =====================================================
-- 8. INITIAL DATA SETUP
-- =====================================================

-- Insert default roles
INSERT INTO roles (name, permissions, description) VALUES
('Admin', '["dashboard_view", "equipment_ppm_read", "equipment_ppm_write", "equipment_ppm_delete", "equipment_ppm_import_export", "equipment_ocm_read", "equipment_ocm_write", "equipment_ocm_delete", "equipment_ocm_import_export", "training_manage", "training_read", "training_write", "training_delete", "audit_log_view", "audit_log_export", "user_manage", "settings_read", "settings_manage", "settings_email_test", "backup_manage", "import_data", "export_data"]'::jsonb, 'Full system access'),
('Editor', '["dashboard_view", "equipment_ppm_read", "equipment_ppm_write", "equipment_ppm_delete", "equipment_ppm_import_export", "equipment_ocm_read", "equipment_ocm_write", "equipment_ocm_delete", "equipment_ocm_import_export", "training_manage", "training_read", "training_write", "training_delete", "import_data", "export_data"]'::jsonb, 'Equipment and training management'),
('Viewer', '["dashboard_view", "equipment_ppm_read", "equipment_ocm_read"]'::jsonb, 'Read-only access')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- 9. COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE settings IS 'Application configuration settings stored as key-value pairs with JSONB values';
COMMENT ON TABLE audit_logs IS 'System audit trail for all user actions and system events';
COMMENT ON TABLE push_subscriptions IS 'Web push notification subscriptions for browser notifications';
COMMENT ON TABLE users IS 'User accounts with authentication and role information';
COMMENT ON TABLE roles IS 'Role definitions with associated permissions';

COMMENT ON COLUMN settings.key IS 'Unique setting identifier (e.g., email_notifications_enabled)';
COMMENT ON COLUMN settings.value IS 'Setting value stored as JSONB for flexibility';
COMMENT ON COLUMN settings.category IS 'Setting category for organization (email, notifications, etc.)';

COMMENT ON COLUMN audit_logs.event_type IS 'Type of event (System Startup, Equipment Added, etc.)';
COMMENT ON COLUMN audit_logs.performed_by IS 'User or system that performed the action';
COMMENT ON COLUMN audit_logs.status IS 'Event status (Success, Failed, Warning, Info)';
COMMENT ON COLUMN audit_logs.details IS 'Additional event details as JSONB';

COMMENT ON COLUMN push_subscriptions.endpoint IS 'Push service endpoint URL';
COMMENT ON COLUMN push_subscriptions.p256dh_key IS 'P256DH public key for encryption';
COMMENT ON COLUMN push_subscriptions.auth_key IS 'Authentication secret for push messages';
