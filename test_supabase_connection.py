#!/usr/bin/env python3
"""
Test script for Supabase database connection.
Run this after setting up your Supabase credentials.
"""
import os
import sys
import logging
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_environment_variables():
    """Test if all required environment variables are set."""
    logger.info("🔍 Testing environment variables...")
    
    required_vars = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY', 
        'DATABASE_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == 'your-project-ref.supabase.co' or 'your-' in value:
            missing_vars.append(var)
        else:
            logger.info(f"✅ {var}: {'*' * (len(value) - 10) + value[-10:]}")
    
    if missing_vars:
        logger.error(f"❌ Missing or placeholder values for: {', '.join(missing_vars)}")
        logger.error("Please update your .env file with actual Supabase credentials")
        return False
    
    logger.info("✅ All environment variables are set")
    return True

def test_supabase_client():
    """Test Supabase client connection."""
    logger.info("🔍 Testing Supabase client connection...")
    
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_ANON_KEY')
        
        client = create_client(supabase_url, supabase_key)
        
        # Test with a simple query (this will fail if tables don't exist yet, but connection will work)
        try:
            response = client.table('departments').select('*').limit(1).execute()
            logger.info("✅ Supabase client connection successful")
            logger.info(f"✅ Tables accessible: {len(response.data) >= 0}")
        except Exception as e:
            if "relation" in str(e).lower() or "table" in str(e).lower():
                logger.info("✅ Supabase client connection successful (tables not created yet)")
            else:
                raise e
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Supabase client connection failed: {e}")
        return False

def test_postgresql_connection():
    """Test PostgreSQL connection via SQLAlchemy."""
    logger.info("🔍 Testing PostgreSQL connection...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ PostgreSQL connection successful")
            logger.info(f"✅ Database version: {version[:50]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_database_manager():
    """Test the custom DatabaseManager class."""
    logger.info("🔍 Testing DatabaseManager...")
    
    try:
        # Temporarily set USE_SUPABASE to true for testing
        os.environ['USE_SUPABASE'] = 'true'
        
        from database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # Test connection
        if db_manager.test_connection():
            logger.info("✅ DatabaseManager connection test passed")
        else:
            logger.error("❌ DatabaseManager connection test failed")
            return False
        
        # Test session creation
        session = db_manager.get_session()
        if session:
            logger.info("✅ DatabaseManager session creation successful")
            session.close()
        else:
            logger.error("❌ DatabaseManager session creation failed")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ DatabaseManager test failed: {e}")
        return False

def main():
    """Run all connection tests."""
    logger.info("🚀 Starting Supabase connection tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Supabase Client", test_supabase_client),
        ("PostgreSQL Connection", test_postgresql_connection),
        ("Database Manager", test_database_manager)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    logger.info("=" * 60)
    logger.info(f"📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Supabase is ready for migration.")
        return True
    else:
        logger.error("⚠️  Some tests failed. Please fix the issues before proceeding.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
