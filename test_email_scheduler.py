#!/usr/bin/env python3
"""
Test the email scheduler functionality with Supabase settings
"""

import os
import sys
import logging
from datetime import datetime

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.data_service import DataService
from app.services.email_service import EmailService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('email_scheduler_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_email_scheduler():
    """Test the email scheduler with Supabase settings."""
    try:
        logger.info("=== Testing Email Scheduler with Supabase Settings ===")
        
        # Test 1: Load settings
        logger.info("Step 1: Loading settings...")
        settings = DataService.load_settings()
        logger.info(f"Loaded {len(settings)} settings")
        
        # Check key email settings
        email_enabled = settings.get('email_notifications_enabled', False)
        recipient_email = settings.get('recipient_email', '')
        scheduler_interval = settings.get('scheduler_interval_hours', 24)
        
        logger.info(f"Email notifications enabled: {email_enabled}")
        logger.info(f"Recipient email: {recipient_email}")
        logger.info(f"Scheduler interval: {scheduler_interval} hours")
        
        # Test 2: Initialize EmailService
        logger.info("Step 2: Initializing EmailService...")
        email_service = EmailService()
        logger.info("EmailService initialized successfully")
        
        # Test 3: Check if scheduler can access settings
        logger.info("Step 3: Testing scheduler settings access...")

        # Simulate what the scheduler does - load settings directly
        current_settings = DataService.load_settings()
        if current_settings:
            logger.info("✓ Scheduler can access current settings")
            logger.info(f"Settings keys available: {list(current_settings.keys())}")
        else:
            logger.warning("⚠ Scheduler cannot access settings")
        
        # Test 4: Test email configuration
        logger.info("Step 4: Testing email configuration...")
        
        # Check if email settings are properly configured
        smtp_server = settings.get('smtp_server', '')
        smtp_port = settings.get('smtp_port', 587)
        smtp_username = settings.get('smtp_username', '')
        
        if smtp_server and smtp_username:
            logger.info("✓ Email configuration appears to be set up")
            logger.info(f"SMTP Server: {smtp_server}")
            logger.info(f"SMTP Port: {smtp_port}")
            logger.info(f"SMTP Username: {smtp_username}")
        else:
            logger.info("ℹ Email configuration not fully set up (this is normal for testing)")
        
        # Test 5: Test scheduler timing logic
        logger.info("Step 5: Testing scheduler timing logic...")
        
        use_daily_send_time = settings.get('use_daily_send_time', True)
        email_send_time = settings.get('email_send_time', '07:35')
        
        logger.info(f"Use daily send time: {use_daily_send_time}")
        logger.info(f"Email send time: {email_send_time}")
        
        if use_daily_send_time and email_send_time:
            logger.info("✓ Daily send time configuration is set up")
        else:
            logger.info("ℹ Using interval-based scheduling")
        
        # Test 6: Verify settings persistence
        logger.info("Step 6: Testing settings persistence...")
        
        # Save a test setting
        test_settings = settings.copy()
        test_settings['last_scheduler_test'] = datetime.now().isoformat()
        
        DataService.save_settings(test_settings)
        logger.info("Test setting saved")
        
        # Reload and verify
        reloaded_settings = DataService.load_settings()
        if 'last_scheduler_test' in reloaded_settings:
            logger.info("✓ Settings persistence working correctly")
            
            # Clean up test setting
            del reloaded_settings['last_scheduler_test']
            DataService.save_settings(reloaded_settings)
            logger.info("Test setting cleaned up")
        else:
            logger.warning("⚠ Settings persistence may have issues")
        
        logger.info("=== Email Scheduler Test Completed Successfully ===")
        return True
        
    except Exception as e:
        logger.error(f"Email scheduler test failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=== ALORF BIOMED Email Scheduler Test Started ===")
    
    success = test_email_scheduler()
    
    if success:
        logger.info("✅ Email scheduler test PASSED - Ready for production use")
    else:
        logger.error("❌ Email scheduler test FAILED - Please check the issues above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
