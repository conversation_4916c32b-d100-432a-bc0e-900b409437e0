"""
Database configuration and connection management for ALORF BIOMED System.
Supports both SQLite (legacy) and Supabase PostgreSQL (new).
"""
import os
import logging
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import SQLAlchemyError
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# SQLAlchemy Base
Base = declarative_base()

class DatabaseManager:
    """Manages database connections for both SQLite and Supabase."""
    
    def __init__(self):
        self.use_supabase = os.getenv('USE_SUPABASE', 'false').lower() == 'true'
        self.sqlite_engine = None
        self.postgres_engine = None
        self.supabase_client = None
        self.SessionLocal = None
        
        # Initialize based on configuration
        if self.use_supabase:
            self._init_supabase()
        else:
            self._init_sqlite()
    
    def _init_sqlite(self):
        """Initialize SQLite database connection."""
        try:
            sqlite_url = os.getenv('SQLITE_DATABASE_URL', 'sqlite:///data/hospital_equipment_dev.db')
            self.sqlite_engine = create_engine(
                sqlite_url,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True
            )
            self.SessionLocal = sessionmaker(bind=self.sqlite_engine)
            logger.info("SQLite database initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize SQLite database: {e}")
            raise
    
    def _init_supabase(self):
        """Initialize Supabase PostgreSQL connection."""
        try:
            # Supabase client for API operations
            supabase_url = os.getenv('SUPABASE_URL')
            supabase_key = os.getenv('SUPABASE_ANON_KEY')
            
            if not supabase_url or not supabase_key:
                raise ValueError("SUPABASE_URL and SUPABASE_ANON_KEY must be set")
            
            self.supabase_client = create_client(supabase_url, supabase_key)
            
            # PostgreSQL engine for SQLAlchemy operations
            postgres_url = os.getenv('DATABASE_URL')
            if not postgres_url:
                raise ValueError("DATABASE_URL must be set for PostgreSQL connection")
            
            self.postgres_engine = create_engine(
                postgres_url,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True,
                pool_size=10,
                max_overflow=20
            )
            self.SessionLocal = sessionmaker(bind=self.postgres_engine)
            logger.info("Supabase PostgreSQL database initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Supabase database: {e}")
            raise
    
    def get_session(self):
        """Get a database session."""
        if not self.SessionLocal:
            raise RuntimeError("Database not initialized")
        return self.SessionLocal()
    
    def get_engine(self):
        """Get the database engine."""
        if self.use_supabase:
            return self.postgres_engine
        return self.sqlite_engine
    
    def get_supabase_client(self) -> Optional[Client]:
        """Get Supabase client for API operations."""
        return self.supabase_client
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            engine = self.get_engine()
            with engine.connect() as conn:
                if self.use_supabase:
                    result = conn.execute(text("SELECT version()"))
                else:
                    result = conn.execute(text("SELECT sqlite_version()"))
                version = result.fetchone()[0]
                logger.info(f"Database connection successful. Version: {version}")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def create_tables(self):
        """Create all database tables."""
        try:
            engine = self.get_engine()
            Base.metadata.create_all(bind=engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """Execute raw SQL query."""
        try:
            engine = self.get_engine()
            with engine.connect() as conn:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                conn.commit()
                return result
        except SQLAlchemyError as e:
            logger.error(f"SQL execution failed: {e}")
            raise
    
    def switch_to_supabase(self):
        """Switch from SQLite to Supabase."""
        logger.info("Switching to Supabase database...")
        self.use_supabase = True
        self._init_supabase()
        
        # Update environment variable
        os.environ['USE_SUPABASE'] = 'true'
        
        # Update .env file
        self._update_env_file('USE_SUPABASE', 'true')
        logger.info("Successfully switched to Supabase database")
    
    def switch_to_sqlite(self):
        """Switch back to SQLite (rollback)."""
        logger.info("Rolling back to SQLite database...")
        self.use_supabase = False
        self._init_sqlite()
        
        # Update environment variable
        os.environ['USE_SUPABASE'] = 'false'
        
        # Update .env file
        self._update_env_file('USE_SUPABASE', 'false')
        logger.info("Successfully rolled back to SQLite database")
    
    def _update_env_file(self, key: str, value: str):
        """Update .env file with new value."""
        try:
            env_path = '.env'
            if not os.path.exists(env_path):
                return
            
            with open(env_path, 'r') as f:
                lines = f.readlines()
            
            updated = False
            for i, line in enumerate(lines):
                if line.startswith(f'{key}='):
                    lines[i] = f'{key}={value}\n'
                    updated = True
                    break
            
            if not updated:
                lines.append(f'{key}={value}\n')
            
            with open(env_path, 'w') as f:
                f.writelines(lines)
                
        except Exception as e:
            logger.warning(f"Failed to update .env file: {e}")

# Global database manager instance
db_manager = DatabaseManager()

# Convenience functions
def get_db_session():
    """Get a database session."""
    return db_manager.get_session()

def get_db_engine():
    """Get the database engine."""
    return db_manager.get_engine()

def get_supabase_client():
    """Get Supabase client."""
    return db_manager.get_supabase_client()

def test_db_connection():
    """Test database connection."""
    return db_manager.test_connection()

def create_all_tables():
    """Create all database tables."""
    return db_manager.create_tables()

# Context manager for database sessions
class DatabaseSession:
    """Context manager for database sessions."""
    
    def __init__(self):
        self.session = None
    
    def __enter__(self):
        self.session = get_db_session()
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type:
                self.session.rollback()
            else:
                self.session.commit()
            self.session.close()
