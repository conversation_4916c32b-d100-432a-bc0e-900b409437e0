2025-07-20 15:04:39,497 - INFO - === ALORF BIOMED Final Phase Migration Started ===
2025-07-20 15:04:41,830 - INFO - Supabase client initialized successfully
2025-07-20 15:04:41,830 - INFO - Step 1: Creating database schemas...
2025-07-20 15:04:41,830 - INFO - Executing SQL migration file: supabase_migration_final_phase.sql
2025-07-20 15:04:42,912 - WARNING - Statement 1 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:43,553 - WARNING - Statement 2 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:44,149 - WARNING - Statement 3 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:44,406 - WARNING - Statement 4 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:44,697 - WARNING - Statement 5 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:44,954 - WARNING - Statement 6 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:45,235 - WARNING - Statement 7 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:45,531 - WARNING - Statement 8 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:45,844 - WARNING - Statement 9 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:46,088 - WARNING - Statement 10 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:46,371 - WARNING - Statement 11 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:46,658 - WARNING - Statement 12 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:46,900 - WARNING - Statement 13 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:47,156 - WARNING - Statement 14 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:47,415 - WARNING - Statement 15 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:47,696 - WARNING - Statement 16 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:47,966 - WARNING - Statement 17 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:48,248 - WARNING - Statement 18 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:48,489 - WARNING - Statement 19 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:48,735 - WARNING - Statement 20 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:49,383 - WARNING - Statement 21 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:49,615 - WARNING - Statement 22 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:49,896 - WARNING - Statement 23 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:50,126 - WARNING - Statement 24 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:50,360 - WARNING - Statement 25 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:50,605 - WARNING - Statement 26 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:50,858 - WARNING - Statement 27 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:51,121 - WARNING - Statement 28 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:51,394 - WARNING - Statement 29 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:51,638 - WARNING - Statement 30 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:51,869 - WARNING - Statement 31 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:52,158 - WARNING - Statement 32 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:52,438 - WARNING - Statement 33 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:52,724 - WARNING - Statement 34 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:52,979 - WARNING - Statement 35 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:53,252 - WARNING - Statement 36 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:53,504 - WARNING - Statement 37 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:53,751 - WARNING - Statement 38 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:53,991 - WARNING - Statement 39 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:54,241 - WARNING - Statement 40 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:54,473 - WARNING - Statement 41 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:54,710 - WARNING - Statement 42 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:54,948 - WARNING - Statement 43 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:55,207 - WARNING - Statement 44 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:55,454 - WARNING - Statement 45 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:55,721 - WARNING - Statement 46 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:56,002 - WARNING - Statement 47 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:56,238 - WARNING - Statement 48 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:56,482 - WARNING - Statement 49 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:56,713 - WARNING - Statement 50 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:56,975 - WARNING - Statement 51 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:57,218 - WARNING - Statement 52 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:57,472 - WARNING - Statement 53 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:57,722 - WARNING - Statement 54 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:57,962 - WARNING - Statement 55 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:58,207 - WARNING - Statement 56 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:58,450 - WARNING - Statement 57 failed (may be expected): {'code': 'PGRST202', 'details': 'Searched for the function public.exec_sql with parameter sql or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.', 'hint': None, 'message': 'Could not find the function public.exec_sql(sql) in the schema cache'}
2025-07-20 15:04:58,469 - INFO - SQL migration file executed successfully
2025-07-20 15:04:58,497 - INFO - Step 2: Migrating settings data...
2025-07-20 15:04:58,498 - INFO - Starting settings data migration...
2025-07-20 15:04:58,528 - WARNING - Settings file not found: data\settings.json
2025-07-20 15:04:58,529 - INFO - Step 3: Migrating audit logs...
2025-07-20 15:04:58,530 - INFO - Starting audit logs migration...
2025-07-20 15:04:58,534 - INFO - Loaded 625 audit log entries
2025-07-20 15:04:58,548 - INFO - Migrating batch 1/7 (100 entries)
2025-07-20 15:04:58,917 - ERROR - Failed to migrate audit logs: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': "Could not find the 'description' column of 'audit_logs' in the schema cache"}
2025-07-20 15:04:58,942 - ERROR - Failed to migrate audit logs
