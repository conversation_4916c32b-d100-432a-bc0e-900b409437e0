"""
Supabase-based User model for authentication
Replaces JSON file-based user authentication with Supabase database
"""

import logging
from flask_login import UserMixin
from supabase import create_client, Client
from app.config import Config

logger = logging.getLogger(__name__)

class SupabaseUser(UserMixin):
    """User model that reads from Supabase database instead of JSON files."""
    
    def __init__(self, user_data):
        """Initialize user from Supabase data."""
        self.id = user_data['username']
        self.username = user_data['username']
        self.password_hash = user_data['password_hash']
        self.role = user_data['role']
        self._is_active = user_data.get('is_active', True)
        self.profile_image_url = user_data.get('profile_image_url', None)
        self._permissions = None

    @property
    def is_active(self):
        """Return whether the user is active (required by Flask-Login)."""
        return self._is_active
    
    @property
    def permissions(self):
        """Lazy load permissions from Supabase roles table."""
        if self._permissions is None:
            self._load_permissions()
        return self._permissions
    
    def _load_permissions(self):
        """Load permissions for the user's role from Supabase."""
        try:
            supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
            
            # Get role permissions from Supabase
            result = supabase.table('roles').select('*').eq('name', self.role).execute()
            
            if result.data:
                role_data = result.data[0]
                self._permissions = role_data.get('permissions', [])
                logger.debug(f"Loaded permissions for {self.role}: {self._permissions}")
            else:
                logger.warning(f"Role {self.role} not found in database")
                self._permissions = []
                
        except Exception as e:
            logger.error(f"Error loading permissions for {self.role}: {e}")
            self._permissions = []
    
    def check_password(self, password):
        """Check if the provided password matches the stored hash."""
        logger.debug(f"Checking password for {self.username}")
        
        if not password:
            logger.debug(f"Empty password provided for {self.username}")
            return False
        
        try:
            from werkzeug.security import check_password_hash
            
            # Check if password matches the stored hash
            is_valid = check_password_hash(self.password_hash, password)
            logger.debug(f"Password validation for {self.username}: {'SUCCESS' if is_valid else 'FAILED'}")
            return is_valid
            
        except Exception as e:
            logger.error(f"Error checking password for {self.username}: {e}")
            return False
    
    def has_permission(self, permission_name):
        """Check if the user has the specified permission."""
        has_perm = permission_name in self.permissions
        logger.debug(f"User {self.username} permission '{permission_name}': {has_perm}")
        return has_perm
    
    @classmethod
    def get_user(cls, username):
        """Get a user by username from Supabase database."""
        try:
            supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
            
            # Get user from Supabase
            result = supabase.table('users').select('*').eq('username', username).execute()
            
            if result.data:
                user_data = result.data[0]
                logger.debug(f"Found user in Supabase: {username}")
                return cls(user_data)
            else:
                logger.warning(f"User not found in Supabase: {username}")
                return None
                
        except Exception as e:
            logger.error(f"Error loading user {username} from Supabase: {e}")
            
            # Fallback to JSON file if Supabase fails
            try:
                from app.models.json_user import JSONUser
                logger.info(f"Falling back to JSON user for {username}")
                return JSONUser.get_user(username)
            except Exception as json_error:
                logger.error(f"Fallback to JSON also failed for {username}: {json_error}")
                return None
    
    @classmethod
    def get_all_users(cls):
        """Get all users from Supabase database."""
        try:
            supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
            
            # Get all users from Supabase
            result = supabase.table('users').select('*').execute()
            
            users = []
            for user_data in result.data:
                users.append(cls(user_data))
            
            logger.debug(f"Loaded {len(users)} users from Supabase")
            return users
            
        except Exception as e:
            logger.error(f"Error loading all users from Supabase: {e}")
            return []
    
    @classmethod
    def create_user(cls, username, password, role):
        """Create a new user in Supabase database."""
        try:
            from werkzeug.security import generate_password_hash
            
            supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
            
            # Generate password hash
            password_hash = generate_password_hash(password)
            
            # Create user record
            user_data = {
                'username': username,
                'password_hash': password_hash,
                'role': role,
                'is_active': True
            }
            
            result = supabase.table('users').insert(user_data).execute()
            
            if result.data:
                logger.info(f"Created new user: {username}")
                return cls(result.data[0])
            else:
                logger.error(f"Failed to create user: {username}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating user {username}: {e}")
            return None
    
    def update_password(self, new_password):
        """Update user's password in Supabase database."""
        try:
            from werkzeug.security import generate_password_hash
            
            supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
            
            # Generate new password hash
            new_hash = generate_password_hash(new_password)
            
            # Update in database
            result = supabase.table('users').update({
                'password_hash': new_hash
            }).eq('username', self.username).execute()
            
            if result.data:
                self.password_hash = new_hash
                logger.info(f"Updated password for user: {self.username}")
                return True
            else:
                logger.error(f"Failed to update password for user: {self.username}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating password for {self.username}: {e}")
            return False
    
    def __repr__(self):
        return f'<SupabaseUser {self.username} ({self.role})>'
