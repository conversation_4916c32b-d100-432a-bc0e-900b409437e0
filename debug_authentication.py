#!/usr/bin/env python3
"""
Debug authentication issues after Supabase migration
"""

import os
import sys
import logging
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config
from werkzeug.security import check_password_hash, generate_password_hash

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_user_migration():
    """Check if users were properly migrated to Supabase."""
    try:
        logger.info("=== Checking User Migration ===")
        
        # Check Supabase users
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        users_result = supabase.table('users').select('*').execute()
        
        logger.info(f"Found {len(users_result.data)} users in Supabase database:")
        for user in users_result.data:
            logger.info(f"  - Username: {user['username']}")
            logger.info(f"    Role: {user['role']}")
            logger.info(f"    Active: {user['is_active']}")
            logger.info(f"    Password hash length: {len(user['password_hash'])}")
            logger.info(f"    Created: {user['created_at']}")
            logger.info("")
        
        # Check original JSON file
        json_users_path = Path('app/data/settings.json')
        if json_users_path.exists():
            logger.info("=== Checking Original JSON Users ===")
            with open(json_users_path, 'r') as f:
                settings = json.load(f)
                users_data = settings.get('users', {})
                logger.info(f"Found {len(users_data)} users in JSON file:")
                for username, user_data in users_data.items():
                    logger.info(f"  - Username: {username}")
                    logger.info(f"    Role: {user_data.get('role', 'Unknown')}")
                    logger.info(f"    Password hash length: {len(user_data.get('password_hash', ''))}")
                    logger.info("")
        
        return users_result.data
        
    except Exception as e:
        logger.error(f"Error checking user migration: {e}")
        return []

def test_password_verification():
    """Test password verification with known credentials."""
    try:
        logger.info("=== Testing Password Verification ===")
        
        # Test credentials
        test_username = "admin"
        test_password = "@Xx123456789xX@"
        
        # Get user from Supabase
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        user_result = supabase.table('users').select('*').eq('username', test_username).execute()
        
        if not user_result.data:
            logger.error(f"❌ User '{test_username}' not found in database!")
            return False
        
        user = user_result.data[0]
        stored_hash = user['password_hash']
        
        logger.info(f"Found user: {user['username']}")
        logger.info(f"Stored hash: {stored_hash[:50]}...")
        logger.info(f"Hash method: {'Werkzeug' if stored_hash.startswith('pbkdf2:') else 'Unknown'}")
        
        # Test password verification
        is_valid = check_password_hash(stored_hash, test_password)
        logger.info(f"Password verification result: {is_valid}")
        
        if not is_valid:
            logger.warning("❌ Password verification failed!")
            
            # Try generating a new hash for comparison
            new_hash = generate_password_hash(test_password)
            logger.info(f"New hash for same password: {new_hash[:50]}...")
            
            # Test the new hash
            new_hash_valid = check_password_hash(new_hash, test_password)
            logger.info(f"New hash verification: {new_hash_valid}")
            
            return False
        else:
            logger.info("✅ Password verification successful!")
            return True
        
    except Exception as e:
        logger.error(f"Error testing password verification: {e}")
        return False

def check_authentication_service():
    """Check if authentication service is using Supabase."""
    try:
        logger.info("=== Checking Authentication Service ===")
        
        # Import authentication modules
        from app.services.data_service import DataService
        
        # Check if DataService is loading from Supabase
        settings = DataService.load_settings()
        users_data = settings.get('users', {})
        
        logger.info(f"DataService loaded {len(users_data)} users")
        
        if 'admin' in users_data:
            admin_user = users_data['admin']
            logger.info(f"Admin user found in settings:")
            logger.info(f"  - Role: {admin_user.get('role')}")
            logger.info(f"  - Password hash: {admin_user.get('password_hash', '')[:50]}...")
        else:
            logger.warning("❌ Admin user not found in loaded settings!")
        
        return len(users_data) > 0
        
    except Exception as e:
        logger.error(f"Error checking authentication service: {e}")
        return False

def fix_user_authentication():
    """Fix user authentication by ensuring proper password hashes."""
    try:
        logger.info("=== Fixing User Authentication ===")
        
        # Known user credentials
        users_to_fix = {
            'admin': '@Xx123456789xX@',
            'editor1': 'editor123',
            'editor2': 'editor456', 
            'viewer1': 'viewer123'
        }
        
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        
        for username, password in users_to_fix.items():
            logger.info(f"Fixing user: {username}")
            
            # Generate proper password hash
            new_hash = generate_password_hash(password)
            
            # Update user in database
            result = supabase.table('users').update({
                'password_hash': new_hash
            }).eq('username', username).execute()
            
            if result.data:
                logger.info(f"✅ Updated password hash for {username}")
                
                # Verify the fix
                verification_result = check_password_hash(new_hash, password)
                logger.info(f"✅ Verification test passed: {verification_result}")
            else:
                logger.warning(f"⚠ Failed to update {username}")
        
        logger.info("✅ User authentication fix completed")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing user authentication: {e}")
        return False

def test_login_functionality():
    """Test the complete login functionality."""
    try:
        logger.info("=== Testing Login Functionality ===")
        
        # Test credentials
        test_username = "admin"
        test_password = "@Xx123456789xX@"
        
        # Import authentication functions
        from app.services.data_service import DataService
        from werkzeug.security import check_password_hash
        
        # Load users (should come from Supabase now)
        settings = DataService.load_settings()
        users_data = settings.get('users', {})
        
        logger.info(f"Loaded {len(users_data)} users from DataService")
        
        if test_username not in users_data:
            logger.error(f"❌ User '{test_username}' not found in loaded data!")
            return False
        
        user_data = users_data[test_username]
        stored_hash = user_data.get('password_hash', '')
        
        logger.info(f"Testing login for: {test_username}")
        logger.info(f"Stored hash: {stored_hash[:50]}...")
        
        # Test password verification
        is_valid = check_password_hash(stored_hash, test_password)
        
        if is_valid:
            logger.info("✅ Login test PASSED!")
            logger.info(f"User role: {user_data.get('role')}")
            return True
        else:
            logger.error("❌ Login test FAILED!")
            return False
        
    except Exception as e:
        logger.error(f"Error testing login functionality: {e}")
        return False

def main():
    """Main debugging function."""
    logger.info("=== ALORF BIOMED Authentication Debug Started ===")
    
    # Step 1: Check user migration
    users = check_user_migration()
    
    # Step 2: Test password verification
    password_ok = test_password_verification()
    
    # Step 3: Check authentication service
    auth_service_ok = check_authentication_service()
    
    # Step 4: Fix authentication if needed
    if not password_ok:
        logger.info("Password verification failed, attempting to fix...")
        fix_user_authentication()
        
        # Re-test after fix
        password_ok = test_password_verification()
    
    # Step 5: Test complete login functionality
    login_ok = test_login_functionality()
    
    # Summary
    logger.info("=== Debug Summary ===")
    logger.info(f"Users migrated: {len(users) > 0}")
    logger.info(f"Password verification: {password_ok}")
    logger.info(f"Auth service working: {auth_service_ok}")
    logger.info(f"Login functionality: {login_ok}")
    
    if password_ok and auth_service_ok and login_ok:
        logger.info("✅ Authentication should be working now!")
        return True
    else:
        logger.error("❌ Authentication issues remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
