#!/usr/bin/env python3
"""
Test Flask application login functionality
"""

import os
import sys
import logging
import requests
import time
from threading import Thread

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def start_flask_app():
    """Start the Flask application in a separate thread."""
    try:
        from main import main
        logger.info("Starting Flask application...")
        main()
    except Exception as e:
        logger.error(f"Error starting Flask app: {e}")

def test_flask_login():
    """Test the Flask application login functionality."""
    try:
        logger.info("=== Testing Flask Application Login ===")
        
        # Start Flask app in background
        flask_thread = Thread(target=start_flask_app, daemon=True)
        flask_thread.start()
        
        # Wait for Flask app to start
        logger.info("Waiting for Flask app to start...")
        time.sleep(5)
        
        # Test if the app is running (try both ports)
        app_url = None
        for port in [5001, 5000]:
            try:
                response = requests.get(f'http://localhost:{port}/', timeout=10)
                logger.info(f"Flask app is running on port {port}! Status code: {response.status_code}")
                app_url = f'http://localhost:{port}'
                break
            except requests.exceptions.RequestException:
                continue

        if not app_url:
            logger.error("Flask app not accessible on ports 5000 or 5001")
            return False

        # Test login page
        try:
            login_response = requests.get(f'{app_url}/login', timeout=10)
            logger.info(f"Login page accessible! Status code: {login_response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Login page not accessible: {e}")
            return False
        
        # Test login functionality
        try:
            session = requests.Session()
            
            # Get login page to extract CSRF token if needed
            login_page = session.get(f'{app_url}/login', timeout=10)

            # Attempt login
            login_data = {
                'username': 'admin',
                'password': '@Xx123456789xX@'
            }

            login_result = session.post(f'{app_url}/login', data=login_data, timeout=10)
            
            # Check if login was successful (redirect or success page)
            if login_result.status_code == 200:
                if 'Invalid username or password' in login_result.text:
                    logger.error("❌ Login failed - Invalid credentials message shown")
                    return False
                else:
                    logger.info("✅ Login appears successful (no error message)")
                    return True
            elif login_result.status_code == 302 or login_result.status_code == 303:
                logger.info("✅ Login successful - redirected to dashboard")
                return True
            else:
                logger.warning(f"⚠ Unexpected login response: {login_result.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Error testing login: {e}")
            return False
        
    except Exception as e:
        logger.error(f"Error in Flask login test: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=== ALORF BIOMED Flask Login Test Started ===")
    
    success = test_flask_login()
    
    if success:
        logger.info("✅ Flask login test PASSED!")
        logger.info("\n🎉 AUTHENTICATION IS NOW WORKING!")
        logger.info("\nYou can now access the application at: http://localhost:5001")
        logger.info("Login credentials:")
        logger.info("  Username: admin")
        logger.info("  Password: @Xx123456789xX@")
    else:
        logger.error("❌ Flask login test FAILED!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
