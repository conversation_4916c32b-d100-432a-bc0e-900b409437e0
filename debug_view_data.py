#!/usr/bin/env python3
"""
Debug view data by simulating the list_equipment view
"""

import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.data_service import DataService

def simulate_list_equipment_view(data_type='ppm'):
    """Simulate the list_equipment view function."""
    print(f"=== Simulating list_equipment view for {data_type} ===")
    
    try:
        # Get all data (no pagination) - same as in views.py line 191
        equipment_data = DataService.get_all_entries(data_type)
        print(f"1. DataService.get_all_entries returned: {len(equipment_data)} entries")
        
        # Check if it's a dict (line 193-194)
        if isinstance(equipment_data, dict):
            equipment_data = [equipment_data]
            print("2. Converted dict to list")
        else:
            print("2. Data is already a list")
            
        print(f"3. Final equipment_data length: {len(equipment_data)}")
        
        # Process each item (lines 196-232)
        for i, item in enumerate(equipment_data[:5]):  # Only show first 5 for debugging
            print(f"\n   Entry {i+1}:")
            print(f"     SERIAL: {item.get('SERIAL')}")
            print(f"     Name: {item.get('Name')}")
            print(f"     Department: {item.get('Department')}")
            print(f"     MODEL: {item.get('MODEL')}")
            print(f"     Status: {item.get('Status')}")
            
            # Apply status class (lines 197-202)
            status = item.get('Status', 'N/A').lower()
            item['status_class'] = {
                'overdue': 'danger',
                'upcoming': 'warning',
                'maintained': 'success'
            }.get(status, 'secondary')
            print(f"     status_class: {item['status_class']}")
            
            # Check quarterly data for PPM
            if data_type == 'ppm':
                print(f"     PPM_Q_I: {item.get('PPM_Q_I')}")
                print(f"     PPM_Q_II: {item.get('PPM_Q_II')}")
        
        print(f"\n4. Total records to pass to template: {len(equipment_data)}")
        return equipment_data
        
    except Exception as e:
        print(f"ERROR in simulate_list_equipment_view: {e}")
        return []

def main():
    print("=== Debugging View Data ===")
    
    # Test PPM
    ppm_data = simulate_list_equipment_view('ppm')
    
    # Test OCM
    print("\n" + "="*50)
    ocm_data = simulate_list_equipment_view('ocm')
    
    print(f"\n=== Summary ===")
    print(f"PPM data for template: {len(ppm_data)} entries")
    print(f"OCM data for template: {len(ocm_data)} entries")
    
    if len(ppm_data) < 100:
        print("⚠️  WARNING: PPM data seems limited")
    if len(ocm_data) < 100:
        print("⚠️  WARNING: OCM data seems limited")

if __name__ == "__main__":
    main()
