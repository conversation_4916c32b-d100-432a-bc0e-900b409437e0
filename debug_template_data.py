#!/usr/bin/env python3
"""
Debug template data loading
"""

import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.data_service import DataService

def main():
    print("=== Debugging Template Data Loading ===")
    
    # Test DataService.get_all_entries
    print("\n1. Testing DataService.get_all_entries('ppm')...")
    ppm_entries = DataService.get_all_entries('ppm')
    print(f"   Returned {len(ppm_entries)} PPM entries")
    
    if ppm_entries:
        sample = ppm_entries[0]
        print(f"   Sample PPM entry keys: {list(sample.keys())}")
        print(f"   Sample values:")
        print(f"     SERIAL: {sample.get('SERIAL')}")
        print(f"     Name: {sample.get('Name')}")
        print(f"     Department: {sample.get('Department')}")
        print(f"     MODEL: {sample.get('MODEL')}")
        print(f"     MANUFACTURER: {sample.get('MANUFACTURER')}")
        print(f"     NO: {sample.get('NO')}")
        print(f"     Status: {sample.get('Status')}")
        
        # Check if there are more entries
        if len(ppm_entries) > 1:
            print(f"   Second entry SERIAL: {ppm_entries[1].get('SERIAL')}")
        
        # Check if we have the expected 1000 entries
        if len(ppm_entries) < 100:
            print(f"   ⚠️  WARNING: Only {len(ppm_entries)} entries returned, expected ~1000")
        else:
            print(f"   ✅ Good: {len(ppm_entries)} entries returned")
    
    print("\n2. Testing DataService.get_all_entries('ocm')...")
    ocm_entries = DataService.get_all_entries('ocm')
    print(f"   Returned {len(ocm_entries)} OCM entries")
    
    if ocm_entries:
        sample = ocm_entries[0]
        print(f"   Sample OCM entry keys: {list(sample.keys())}")
        print(f"   Sample values:")
        print(f"     Serial: {sample.get('Serial')}")
        print(f"     Name: {sample.get('Name')}")
        print(f"     Department: {sample.get('Department')}")
        print(f"     MODEL: {sample.get('MODEL')}")
        print(f"     NO: {sample.get('NO')}")
        print(f"     Status: {sample.get('Status')}")
    
    print("\n3. Testing DataService.load_data directly...")
    ppm_raw = DataService.load_data('ppm')
    print(f"   Raw PPM data: {len(ppm_raw)} entries")
    
    if ppm_raw:
        sample = ppm_raw[0]
        print(f"   Raw PPM sample keys: {list(sample.keys())}")
        print(f"   Raw PPM sample values:")
        print(f"     SERIAL: {sample.get('SERIAL')}")
        print(f"     Name: {sample.get('Name')}")
        print(f"     Department: {sample.get('Department')}")
        print(f"     Raw serial: {sample.get('serial')}")
        print(f"     Raw name: {sample.get('name')}")
        print(f"     Raw department: {sample.get('department')}")

if __name__ == "__main__":
    main()
