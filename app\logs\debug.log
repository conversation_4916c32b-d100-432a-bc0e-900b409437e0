{"timestamp":"2025-07-19T21:25:56.396430Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":19944,"process":3556}
{"timestamp":"2025-07-19T21:25:56.400420Z","level":"DEBUG","module":"app.services.email_service","message":"Loaded settings: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"run_scheduler_async_loop","line":55,"thread":19944,"process":3556}
{"timestamp":"2025-07-19T21:25:56.400420Z","level":"INFO","module":"app.services.email_service","message":"Email notifications are ENABLED in settings. Processing reminders.","function":"run_scheduler_async_loop","line":74,"thread":19944,"process":3556}
{"timestamp":"2025-07-19T21:25:56.401417Z","level":"INFO","module":"app.services.email_service","message":"Next email scheduled for 2025-07-20 07:30:00. Sleeping for 60.0 minutes.","function":"run_scheduler_async_loop","line":123,"thread":19944,"process":3556}
{"timestamp":"2025-07-19T21:26:11.399147Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Initial delay complete. Starting main loop.","function":"run_scheduler_async_loop","line":105,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.400126Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Loop iteration started.","function":"run_scheduler_async_loop","line":108,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.401128Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Loading settings...","function":"run_scheduler_async_loop","line":111,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.401128Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to load settings.","function":"load_settings","line":81,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.402151Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.403149Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.403149Z","level":"DEBUG","module":"app.services.data_service","message":"Reading settings from file: data\\settings.json","function":"load_settings","line":94,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.408107Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.415088Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Loaded settings: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"run_scheduler_async_loop","line":113,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.418080Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Push notifications enabled: True","function":"run_scheduler_async_loop","line":122,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.418080Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Interval (minutes): 60","function":"run_scheduler_async_loop","line":124,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.419077Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Interval (seconds): 3600","function":"run_scheduler_async_loop","line":131,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.420075Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Push notifications are ENABLED. Processing...","function":"run_scheduler_async_loop","line":134,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.423067Z","level":"INFO","module":"app.services.push_notification_service","message":"Starting process_push_notifications method.","function":"process_push_notifications","line":282,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.424064Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Reloading settings within process_push_notifications...","function":"process_push_notifications","line":286,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.425061Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to load settings.","function":"load_settings","line":81,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.425061Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.427056Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.428054Z","level":"DEBUG","module":"app.services.data_service","message":"Reading settings from file: data\\settings.json","function":"load_settings","line":94,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.434038Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.438027Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Settings loaded in process_push_notifications: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"process_push_notifications","line":288,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.440022Z","level":"INFO","module":"app.services.push_notification_service","message":"Push notifications are enabled. Proceeding with data loading and processing.","function":"process_push_notifications","line":293,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.441019Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loading PPM data...","function":"process_push_notifications","line":296,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.441019Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ppm'","function":"load_data","line":152,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.442016Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.443012Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.443012Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.444009Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.445007Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.446004Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.447002Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.447002Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.447999Z","level":"DEBUG","module":"app.services.data_service","message":"Selected PPM file path: data\\ppm.json","function":"load_data","line":161,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.447999Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ppm.json","function":"load_data","line":169,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.448996Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ppm.json","function":"load_data","line":171,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.463957Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.463957Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.485928Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.487894Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4A', 'Name': 'CTG MACHINE', 'MODEL': 'F3', 'SERIAL': '560034-EM18A05070007', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2456', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '27/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '25/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '23/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4A', 'Name': 'ECG MACHINE', 'MODEL': 'SE1200EXPRESS', 'SERIAL': '460016-M19902-', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2452', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '24/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '20/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '19/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.487894Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 1040 entries","function":"load_data","line":189,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.488890Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loaded 1040 PPM entries.","function":"process_push_notifications","line":298,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.489888Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loading OCM data...","function":"process_push_notifications","line":300,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.489888Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ocm'","function":"load_data","line":152,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.490885Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.490885Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.491882Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.492878Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.493877Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.494874Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.495872Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.495872Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.496869Z","level":"DEBUG","module":"app.services.data_service","message":"Selected OCM file path: data\\ocm.json","function":"load_data","line":164,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.496869Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ocm.json","function":"load_data","line":169,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.497882Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ocm.json","function":"load_data","line":171,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.503851Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.504846Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.516814Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.516814Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SW3266-', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2394', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/10/2025', 'Engineer': 'NIXON', 'Next_Maintenance': '01/10/2026', 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SX2315', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2397', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/11/2025', 'Engineer': 'MANU', 'Next_Maintenance': '01/11/2026', 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.517811Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 612 entries","function":"load_data","line":189,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.518809Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loaded 612 OCM entries.","function":"process_push_notifications","line":302,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.519809Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Getting upcoming PPM maintenance tasks...","function":"process_push_notifications","line":304,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.627516Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Found 746 upcoming PPM tasks.","function":"process_push_notifications","line":308,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.628514Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Getting upcoming OCM maintenance tasks...","function":"process_push_notifications","line":310,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.638487Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Found 1 upcoming OCM tasks.","function":"process_push_notifications","line":312,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.639484Z","level":"INFO","module":"app.services.push_notification_service","message":"Total upcoming maintenance tasks (PPM & OCM): 747","function":"process_push_notifications","line":315,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.639484Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Sorting all upcoming tasks by date...","function":"process_push_notifications","line":318,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.652479Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Tasks sorted.","function":"process_push_notifications","line":321,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.652479Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Summarizing all upcoming tasks...","function":"process_push_notifications","line":323,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.652479Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Summarizing upcoming maintenance. Tasks received: 747","function":"summarize_upcoming_maintenance","line":162,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.652479Z","level":"DEBUG","module":"app.services.push_notification_service","message":"PPM count: 746, OCM count: 1","function":"summarize_upcoming_maintenance","line":169,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.653479Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Generated summary: 746 PPM tasks and 1 OCM task due soon.","function":"summarize_upcoming_maintenance","line":182,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.653479Z","level":"INFO","module":"app.services.push_notification_service","message":"Generated summary for push notification: '746 PPM tasks and 1 OCM task due soon.'","function":"process_push_notifications","line":325,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.654445Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Sending push notification with the generated summary...","function":"process_push_notifications","line":327,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.654445Z","level":"INFO","module":"app.services.push_notification_service","message":"Attempting to send push notification with summary: '746 PPM tasks and 1 OCM task due soon.'","function":"send_push_notification","line":191,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.654445Z","level":"DEBUG","module":"app.services.push_notification_service","message":"VAPID configuration seems present.","function":"send_push_notification","line":200,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.655443Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loading push subscriptions...","function":"send_push_notification","line":202,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.655443Z","level":"INFO","module":"app.services.push_notification_service","message":"No push subscriptions found. Nothing to send.","function":"send_push_notification","line":205,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.656441Z","level":"INFO","module":"app.services.push_notification_service","message":"Push notification sending process initiated from process_push_notifications.","function":"process_push_notifications","line":329,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.657437Z","level":"INFO","module":"app.services.push_notification_service","message":"Finished process_push_notifications method.","function":"process_push_notifications","line":334,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.658464Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Finished processing push notifications for this iteration.","function":"run_scheduler_async_loop","line":137,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:26:11.659432Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Sleeping for 60 minutes (3600 seconds).","function":"run_scheduler_async_loop","line":143,"thread":18884,"process":3556}
{"timestamp":"2025-07-19T21:45:07.101156Z","level":"INFO","module":"app","message":"Enhanced logging service initialized","function":"create_app","line":124,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.505432Z","level":"DEBUG","module":"app.services.email_service","message":"Initializing EmailService","function":"EmailService","line":22,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.552277Z","level":"DEBUG","module":"app","message":"Initializing import/export service","function":"<module>","line":23,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.559259Z","level":"DEBUG","module":"app","message":"[app.decorators] Logging started for decorators.py","function":"<module>","line":10,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.560256Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['dashboard_view']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.560256Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_read', 'equipment_ocm_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.560256Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.560256Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.560256Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.561253Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.561253Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_delete', 'equipment_ocm_delete']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.561253Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_import_export', 'equipment_ocm_import_export']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.561253Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_import_export', 'equipment_ocm_import_export']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.562250Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_import_export', 'equipment_ocm_import_export']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.562250Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_import_export', 'equipment_ocm_import_export', 'training_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.562250Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.562250Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.562250Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.562250Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_email_test']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.563247Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.563247Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.563247Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.563247Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.563247Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.563247Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.564245Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.564245Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['dashboard_view']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.564245Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['audit_log_view']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.564245Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['audit_log_export']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.564245Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.565243Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.565243Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.565243Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.565243Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.565243Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['user_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.567237Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_read', 'equipment_ocm_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.567237Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_read', 'equipment_ocm_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.567237Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.568234Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.568234Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_delete', 'equipment_ocm_delete']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.568234Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['export_data']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.568234Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['import_data']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.568234Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_delete', 'equipment_ocm_delete']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.568234Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.569232Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.569232Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.569232Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.569232Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_delete']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.569232Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['training_delete']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.569232Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.570229Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.570229Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.570229Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.570229Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.570229Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.570229Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['backup_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.571226Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_read', 'equipment_ocm_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.571226Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.571226Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.571226Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_read', 'equipment_ocm_read']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.571226Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['equipment_ppm_write', 'equipment_ocm_write']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.572224Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['dashboard_view']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.573221Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.573221Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.573221Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.574220Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.574220Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['settings_manage']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.574220Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['dashboard_view']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.574220Z","level":"DEBUG","module":"app","message":"[app.decorators] Applying permission_required decorator for permission: ['dashboard_view']","function":"permission_required","line":19,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.575216Z","level":"DEBUG","module":"app","message":"[app.routes.auth] Logging started for auth.py","function":"<module>","line":12,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.575216Z","level":"DEBUG","module":"app","message":"[app.routes.auth] Auth blueprint initialized","function":"<module>","line":15,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.632064Z","level":"INFO","module":"app","message":"Email scheduler thread initiated from create_app.","function":"create_app","line":163,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.635056Z","level":"INFO","module":"app","message":"Push Notification scheduler thread initiated from create_app.","function":"create_app","line":169,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.637051Z","level":"INFO","module":"app.services.backup_service","message":"Backup scheduler loop started","function":"_run_backup_scheduler","line":734,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.639045Z","level":"INFO","module":"app.services.backup_service","message":"Automatic backup scheduler started","function":"start_automatic_backup_scheduler","line":717,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.639045Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to load settings.","function":"load_settings","line":81,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.640057Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.641041Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.641041Z","level":"INFO","module":"app","message":"Automatic backup scheduler initiated from create_app.","function":"create_app","line":174,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.641041Z","level":"DEBUG","module":"app.services.data_service","message":"Reading settings from file: data\\settings.json","function":"load_settings","line":94,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.642038Z","level":"INFO","module":"app","message":"Application initialization complete","function":"create_app","line":178,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.645029Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.645029Z","level":"INFO","module":"app","message":"Attempting to update PPM statuses on application startup...","function":"create_app","line":184,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.647025Z","level":"INFO","module":"app.services.backup_service","message":"Automatic backup is enabled, creating backup every 24 hours","function":"_run_backup_scheduler","line":746,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.648023Z","level":"INFO","module":"app.services.data_service","message":"Starting update of all PPM statuses.","function":"update_all_ppm_statuses","line":1173,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.650017Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ppm'","function":"load_data","line":152,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.650017Z","level":"INFO","module":"app.services.backup_service","message":"Backup directories initialized successfully","function":"initialize_backup_directories","line":76,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:08.651014Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.651014Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.653010Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.653010Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.655003Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.656002Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.656998Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.656998Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.657995Z","level":"DEBUG","module":"app.services.data_service","message":"Selected PPM file path: data\\ppm.json","function":"load_data","line":161,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.657995Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ppm.json","function":"load_data","line":169,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.658992Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ppm.json","function":"load_data","line":171,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.663978Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.664975Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.672956Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.673953Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4A', 'Name': 'CTG MACHINE', 'MODEL': 'F3', 'SERIAL': '560034-EM18A05070007', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2456', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '27/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '25/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '23/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4A', 'Name': 'ECG MACHINE', 'MODEL': 'SE1200EXPRESS', 'SERIAL': '460016-M19902-', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2452', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '24/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '20/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '19/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.673953Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 1040 entries","function":"load_data","line":189,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.718833Z","level":"INFO","module":"app.services.data_service","message":"No PPM statuses required updating.","function":"update_all_ppm_statuses","line":1201,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.722821Z","level":"INFO","module":"app","message":"PPM statuses update process completed.","function":"create_app","line":186,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.759724Z","level":"INFO","module":"werkzeug","message":"\u001b[31m\u001b[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.\u001b[0m\n * Running on all addresses (0.0.0.0)\n * Running on http://127.0.0.1:5001\n * Running on http://*************:5001","function":"_log","line":97,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.760721Z","level":"INFO","module":"werkzeug","message":"\u001b[33mPress CTRL+C to quit\u001b[0m","function":"_log","line":97,"thread":18872,"process":12836}
{"timestamp":"2025-07-19T21:45:08.947222Z","level":"INFO","module":"app.services.push_notification_service","message":"PushNotificationService class loading.","function":"PushNotificationService","line":35,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:08.990107Z","level":"DEBUG","module":"asyncio","message":"Using proactor: IocpProactor","function":"__init__","line":634,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:08.991104Z","level":"DEBUG","module":"asyncio","message":"Using proactor: IocpProactor","function":"__init__","line":634,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:09.025013Z","level":"INFO","module":"app","message":"Background thread for scheduler is starting.","function":"start_email_scheduler","line":46,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:09.029004Z","level":"INFO","module":"app","message":"Background thread for Push Notification scheduler is starting.","function":"start_push_notification_scheduler","line":71,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:09.030001Z","level":"INFO","module":"app.services.email_service","message":"Email reminder scheduler async loop started in process ID: 12836.","function":"run_scheduler_async_loop","line":41,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:09.030001Z","level":"INFO","module":"app.services.push_notification_service","message":"Attempting to start Push Notification Scheduler async loop.","function":"run_scheduler_async_loop","line":80,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:09.033990Z","level":"WARNING","module":"app.services.email_service","message":"If running multiple application instances (e.g., Gunicorn workers), ensure this scheduler is enabled in only ONE instance to avoid duplicate emails.","function":"run_scheduler_async_loop","line":42,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:09.077873Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification reminder scheduler async loop initiated in process ID: 12836.","function":"run_scheduler_async_loop","line":97,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:09.081861Z","level":"INFO","module":"app.services.email_service","message":"Scheduler starting. Initial delay of 30 seconds before first run.","function":"run_scheduler_async_loop","line":47,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:09.082859Z","level":"WARNING","module":"app.services.push_notification_service","message":"If running multiple application instances (e.g., Gunicorn workers), ensure this scheduler is enabled in only ONE instance if push logic isn't idempotent or if it involves external state not designed for concurrency.","function":"run_scheduler_async_loop","line":98,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:09.083858Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Initial delay of 45 seconds before first run.","function":"run_scheduler_async_loop","line":103,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:09.173616Z","level":"DEBUG","module":"app.services.audit_service","message":"Loaded 609 audit log entries","function":"_load_logs","line":155,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.177605Z","level":"DEBUG","module":"app.services.audit_service","message":"Loaded 609 audit log entries","function":"_load_logs","line":155,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.193563Z","level":"DEBUG","module":"app.services.audit_service","message":"Saved 610 audit log entries","function":"_save_logs","line":176,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.194560Z","level":"INFO","module":"app.services.audit_service","message":"Audit event logged: Backup Created by System","function":"log_event","line":231,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.195557Z","level":"INFO","module":"app.services.backup_service","message":"Full backup created successfully: full_backup_20250720_004508_652012.zip (0.23 MB)","function":"create_full_backup","line":174,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.195557Z","level":"INFO","module":"app.services.backup_service","message":"Automatic full backup created: full_backup_20250720_004508_652012.zip (0.23 MB)","function":"_run_backup_scheduler","line":751,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.210518Z","level":"DEBUG","module":"app.services.audit_service","message":"Loaded 610 audit log entries","function":"_load_logs","line":155,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.214507Z","level":"DEBUG","module":"app.services.audit_service","message":"Loaded 610 audit log entries","function":"_load_logs","line":155,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.230465Z","level":"DEBUG","module":"app.services.audit_service","message":"Saved 611 audit log entries","function":"_save_logs","line":176,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.231462Z","level":"INFO","module":"app.services.audit_service","message":"Audit event logged: Backup Created by System","function":"log_event","line":231,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.232459Z","level":"INFO","module":"app.services.backup_service","message":"Backup directories initialized successfully","function":"initialize_backup_directories","line":76,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.234454Z","level":"INFO","module":"app.services.backup_service","message":"Cleanup completed: 0 old backups deleted","function":"cleanup_old_backups","line":690,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:09.235451Z","level":"INFO","module":"app.services.backup_service","message":"Backup scheduler sleeping for 24 hours (86400 seconds)","function":"_run_backup_scheduler","line":780,"thread":12368,"process":12836}
{"timestamp":"2025-07-19T21:45:39.091972Z","level":"DEBUG","module":"app.services.email_service","message":"Scheduler loop iteration started.","function":"run_scheduler_async_loop","line":51,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.092999Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to load settings.","function":"load_settings","line":81,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.092999Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.093996Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.094994Z","level":"DEBUG","module":"app.services.data_service","message":"Reading settings from file: data\\settings.json","function":"load_settings","line":94,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.098949Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.101980Z","level":"DEBUG","module":"app.services.email_service","message":"Loaded settings: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"run_scheduler_async_loop","line":55,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.102970Z","level":"INFO","module":"app.services.email_service","message":"Email notifications are ENABLED in settings. Processing reminders.","function":"run_scheduler_async_loop","line":74,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:39.104934Z","level":"INFO","module":"app.services.email_service","message":"Next email scheduled for 2025-07-20 07:30:00. Sleeping for 60.0 minutes.","function":"run_scheduler_async_loop","line":123,"thread":17476,"process":12836}
{"timestamp":"2025-07-19T21:45:54.092765Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Initial delay complete. Starting main loop.","function":"run_scheduler_async_loop","line":105,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.093763Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Loop iteration started.","function":"run_scheduler_async_loop","line":108,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.093763Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Loading settings...","function":"run_scheduler_async_loop","line":111,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.093763Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to load settings.","function":"load_settings","line":81,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.093763Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.093763Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.094760Z","level":"DEBUG","module":"app.services.data_service","message":"Reading settings from file: data\\settings.json","function":"load_settings","line":94,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.095765Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.097753Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Loaded settings: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"run_scheduler_async_loop","line":113,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.098750Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Push notifications enabled: True","function":"run_scheduler_async_loop","line":122,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.098750Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Interval (minutes): 60","function":"run_scheduler_async_loop","line":124,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.098750Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Interval (seconds): 3600","function":"run_scheduler_async_loop","line":131,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.098750Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Push notifications are ENABLED. Processing...","function":"run_scheduler_async_loop","line":134,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.099748Z","level":"INFO","module":"app.services.push_notification_service","message":"Starting process_push_notifications method.","function":"process_push_notifications","line":282,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.099748Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Reloading settings within process_push_notifications...","function":"process_push_notifications","line":286,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.100752Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to load settings.","function":"load_settings","line":81,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.100752Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.100752Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.100752Z","level":"DEBUG","module":"app.services.data_service","message":"Reading settings from file: data\\settings.json","function":"load_settings","line":94,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.102739Z","level":"INFO","module":"app.services.data_service","message":"Successfully loaded settings from data\\settings.json: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"load_settings","line":102,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.103737Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Settings loaded in process_push_notifications: {'email_notifications_enabled': True, 'email_reminder_interval_minutes': 60, 'recipient_email': '<EMAIL>', 'push_notifications_enabled': True, 'push_notification_interval_minutes': 60, 'email_send_time_hour': 7, 'reminder_timing': {'60_days_before': False, '14_days_before': False, '1_day_before': False}, 'scheduler_interval_hours': 24, 'enable_automatic_reminders': True, 'cc_emails': '<EMAIL>', 'automatic_backup_enabled': True, 'automatic_backup_interval_hours': 24, 'users': [{'username': 'admin', 'password': 'pbkdf2:sha256:600000$PRPn88H1ijaWaJ14$4f30ec776a03728db5c793ebba0e29149903c43e37b18e710ecf6dd0553ecb25', 'role': 'Admin'}, {'username': 'testuser', 'password': 'scrypt:32768:8:1$81Env3h8t2d0j8on$3a9c4ad7bfb0650998f343b9b45e86024f114c3be41b2b12a9e04b59628c6872e88901b87212c2cdc233755f12a8ac1bb8bfe2df6ef9bc981ca1220e252b30ba', 'role': 'Admin'}], 'roles': {'Admin': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']}, 'Editor': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'import_data', 'export_data']}, 'Viewer': {'permissions': ['dashboard_view', 'equipment_ppm_read', 'equipment_ocm_read']}}, 'daily_send_time_enabled': True, 'legacy_interval_enabled': False, 'email_send_time_minute': 38, 'email_send_time': '07:30', 'use_daily_send_time': True, 'use_legacy_interval': False}","function":"process_push_notifications","line":288,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.104734Z","level":"INFO","module":"app.services.push_notification_service","message":"Push notifications are enabled. Proceeding with data loading and processing.","function":"process_push_notifications","line":293,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.104734Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loading PPM data...","function":"process_push_notifications","line":296,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.105741Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ppm'","function":"load_data","line":152,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.105741Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.105741Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.105741Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.106728Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.106728Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.107728Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.107728Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.107728Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.107728Z","level":"DEBUG","module":"app.services.data_service","message":"Selected PPM file path: data\\ppm.json","function":"load_data","line":161,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.108723Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ppm.json","function":"load_data","line":169,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.109721Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ppm.json","function":"load_data","line":171,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.114707Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.114707Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.122686Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.123683Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4A', 'Name': 'CTG MACHINE', 'MODEL': 'F3', 'SERIAL': '560034-EM18A05070007', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2456', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '27/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '25/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '23/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4A', 'Name': 'ECG MACHINE', 'MODEL': 'SE1200EXPRESS', 'SERIAL': '460016-M19902-', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2452', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '24/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '20/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '19/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.123683Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 1040 entries","function":"load_data","line":189,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.124682Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loaded 1040 PPM entries.","function":"process_push_notifications","line":298,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.124682Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loading OCM data...","function":"process_push_notifications","line":300,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.125679Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ocm'","function":"load_data","line":152,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.125679Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.125679Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.125679Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.126675Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.126675Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.126675Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.126675Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.127672Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.127672Z","level":"DEBUG","module":"app.services.data_service","message":"Selected OCM file path: data\\ocm.json","function":"load_data","line":164,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.127672Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ocm.json","function":"load_data","line":169,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.127672Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ocm.json","function":"load_data","line":171,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.129667Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.129667Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.132659Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.132659Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SW3266-', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2394', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/10/2025', 'Engineer': 'NIXON', 'Next_Maintenance': '01/10/2026', 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SX2315', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2397', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/11/2025', 'Engineer': 'MANU', 'Next_Maintenance': '01/11/2026', 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.133656Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 612 entries","function":"load_data","line":189,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.133656Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loaded 612 OCM entries.","function":"process_push_notifications","line":302,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.133656Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Getting upcoming PPM maintenance tasks...","function":"process_push_notifications","line":304,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.228402Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Found 746 upcoming PPM tasks.","function":"process_push_notifications","line":308,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.229400Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Getting upcoming OCM maintenance tasks...","function":"process_push_notifications","line":310,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.240371Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Found 1 upcoming OCM tasks.","function":"process_push_notifications","line":312,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.241369Z","level":"INFO","module":"app.services.push_notification_service","message":"Total upcoming maintenance tasks (PPM & OCM): 747","function":"process_push_notifications","line":315,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.242367Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Sorting all upcoming tasks by date...","function":"process_push_notifications","line":318,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.255330Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Tasks sorted.","function":"process_push_notifications","line":321,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.255330Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Summarizing all upcoming tasks...","function":"process_push_notifications","line":323,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.255330Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Summarizing upcoming maintenance. Tasks received: 747","function":"summarize_upcoming_maintenance","line":162,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.255330Z","level":"DEBUG","module":"app.services.push_notification_service","message":"PPM count: 746, OCM count: 1","function":"summarize_upcoming_maintenance","line":169,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.255330Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Generated summary: 746 PPM tasks and 1 OCM task due soon.","function":"summarize_upcoming_maintenance","line":182,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.256328Z","level":"INFO","module":"app.services.push_notification_service","message":"Generated summary for push notification: '746 PPM tasks and 1 OCM task due soon.'","function":"process_push_notifications","line":325,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.257331Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Sending push notification with the generated summary...","function":"process_push_notifications","line":327,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.259322Z","level":"INFO","module":"app.services.push_notification_service","message":"Attempting to send push notification with summary: '746 PPM tasks and 1 OCM task due soon.'","function":"send_push_notification","line":191,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.259322Z","level":"DEBUG","module":"app.services.push_notification_service","message":"VAPID configuration seems present.","function":"send_push_notification","line":200,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.259322Z","level":"DEBUG","module":"app.services.push_notification_service","message":"Loading push subscriptions...","function":"send_push_notification","line":202,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.260318Z","level":"INFO","module":"app.services.push_notification_service","message":"No push subscriptions found. Nothing to send.","function":"send_push_notification","line":205,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.261316Z","level":"INFO","module":"app.services.push_notification_service","message":"Push notification sending process initiated from process_push_notifications.","function":"process_push_notifications","line":329,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.261316Z","level":"INFO","module":"app.services.push_notification_service","message":"Finished process_push_notifications method.","function":"process_push_notifications","line":334,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.263310Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Finished processing push notifications for this iteration.","function":"run_scheduler_async_loop","line":137,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:45:54.264307Z","level":"INFO","module":"app.services.push_notification_service","message":"Push Notification Scheduler: Sleeping for 60 minutes (3600 seconds).","function":"run_scheduler_async_loop","line":143,"thread":11484,"process":12836}
{"timestamp":"2025-07-19T21:49:03.346868Z","level":"DEBUG","module":"app","message":"[app.decorators] Checking permission: ['dashboard_view']","function":"decorated_function","line":24,"thread":11516,"process":12836,"request":{"method":"HEAD","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.5624","referrer":null}}
{"timestamp":"2025-07-19T21:49:03.349857Z","level":"WARNING","module":"app","message":"[app.decorators] Unauthenticated user attempted access","function":"decorated_function","line":26,"thread":11516,"process":12836,"request":{"method":"HEAD","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.5624","referrer":null}}
{"timestamp":"2025-07-19T21:49:03.353845Z","level":"INFO","module":"app","message":"[app.decorators] Redirecting unauthenticated user to login page","function":"decorated_function","line":32,"thread":11516,"process":12836,"request":{"method":"HEAD","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.5624","referrer":null}}
{"timestamp":"2025-07-19T21:49:03.375788Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:03] \"\u001b[32mHEAD / HTTP/1.1\u001b[0m\" 302 -","function":"_log","line":97,"thread":11516,"process":12836}
{"timestamp":"2025-07-19T21:49:03.410691Z","level":"INFO","module":"app","message":"[app.routes.auth] Accessing login route","function":"login","line":20,"thread":18048,"process":12836,"request":{"method":"HEAD","url":"http://localhost:5001/auth/login?next=http://localhost:5001/","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.5624","referrer":null}}
{"timestamp":"2025-07-19T21:49:03.476517Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:03] \"HEAD /auth/login?next=http://localhost:5001/ HTTP/1.1\" 200 -","function":"_log","line":97,"thread":18048,"process":12836}
{"timestamp":"2025-07-19T21:49:55.974038Z","level":"DEBUG","module":"app","message":"[app.decorators] Checking permission: ['dashboard_view']","function":"decorated_function","line":24,"thread":17404,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:49:55.975036Z","level":"WARNING","module":"app","message":"[app.decorators] Unauthenticated user attempted access","function":"decorated_function","line":26,"thread":17404,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:49:55.977031Z","level":"INFO","module":"app","message":"[app.decorators] Redirecting unauthenticated user to login page","function":"decorated_function","line":32,"thread":17404,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:49:55.983014Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:55] \"\u001b[32mGET / HTTP/1.1\u001b[0m\" 302 -","function":"_log","line":97,"thread":17404,"process":12836}
{"timestamp":"2025-07-19T21:49:56.000966Z","level":"INFO","module":"app","message":"[app.routes.auth] Accessing login route","function":"login","line":20,"thread":10796,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/auth/login?next=http://localhost:5001/","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:49:56.016924Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:56] \"GET /auth/login?next=http://localhost:5001/ HTTP/1.1\" 200 -","function":"_log","line":97,"thread":10796,"process":12836}
{"timestamp":"2025-07-19T21:49:56.377964Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:56] \"\u001b[36mGET /static/css/normalize.css HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":20172,"process":12836}
{"timestamp":"2025-07-19T21:49:56.488662Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:56] \"\u001b[36mGET /static/css/main.css HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":3556,"process":12836}
{"timestamp":"2025-07-19T21:49:56.810804Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:56] \"\u001b[36mGET /static/images/login-background.jpg HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":19472,"process":12836}
{"timestamp":"2025-07-19T21:49:58.362654Z","level":"INFO","module":"app","message":"[app.routes.auth] Accessing login route","function":"login","line":20,"thread":13856,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:58.363652Z","level":"INFO","module":"app","message":"[app.routes.auth] Login attempt for username: admin","function":"login","line":29,"thread":13856,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:58.364649Z","level":"DEBUG","module":"app","message":"[app.routes.auth] Retrieved user: admin","function":"login","line":33,"thread":13856,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:58.364649Z","level":"DEBUG","module":"app","message":"[check_password] Checking password for admin","function":"check_password","line":50,"thread":13856,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.084723Z","level":"INFO","module":"app","message":"[app.routes.auth] Accessing login route","function":"login","line":20,"thread":20324,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.093705Z","level":"INFO","module":"app","message":"[app.routes.auth] Login attempt for username: admin","function":"login","line":29,"thread":20324,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.095694Z","level":"DEBUG","module":"app","message":"[app.routes.auth] Retrieved user: admin","function":"login","line":33,"thread":20324,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.095694Z","level":"DEBUG","module":"app","message":"[check_password] Checking password for admin","function":"check_password","line":50,"thread":20324,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.380934Z","level":"DEBUG","module":"app","message":"[check_password] Password validation for admin: SUCCESS","function":"check_password","line":62,"thread":13856,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.381930Z","level":"INFO","module":"app","message":"[app.routes.auth] User 'admin' logged in successfully.","function":"login","line":37,"thread":13856,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:49:59.384921Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:49:59] \"\u001b[32mPOST /auth/login HTTP/1.1\u001b[0m\" 303 -","function":"_log","line":97,"thread":13856,"process":12836}
{"timestamp":"2025-07-19T21:50:00.085050Z","level":"DEBUG","module":"app","message":"[check_password] Password validation for admin: SUCCESS","function":"check_password","line":62,"thread":20324,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.086048Z","level":"INFO","module":"app","message":"[app.routes.auth] User 'admin' logged in successfully.","function":"login","line":37,"thread":20324,"process":12836,"request":{"method":"POST","url":"http://localhost:5001/auth/login","endpoint":"auth.login","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.089040Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:00] \"\u001b[32mPOST /auth/login HTTP/1.1\u001b[0m\" 303 -","function":"_log","line":97,"thread":20324,"process":12836}
{"timestamp":"2025-07-19T21:50:00.107988Z","level":"DEBUG","module":"app","message":"[app.decorators] Checking permission: ['dashboard_view']","function":"decorated_function","line":24,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.109984Z","level":"DEBUG","module":"app","message":"[app.models.json_user] Loaded permissions for Admin: ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']","function":"_load_permissions","line":43,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.109984Z","level":"DEBUG","module":"app","message":"[app.decorators] Permission check result for ['dashboard_view']: True","function":"decorated_function","line":52,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.111000Z","level":"DEBUG","module":"app","message":"[app.decorators] Permission granted","function":"decorated_function","line":63,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.111000Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ppm'","function":"load_data","line":152,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.111980Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.111980Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.111980Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.112989Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.113978Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.113978Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.114970Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.114970Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.114970Z","level":"DEBUG","module":"app.services.data_service","message":"Selected PPM file path: data\\ppm.json","function":"load_data","line":161,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.114970Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ppm.json","function":"load_data","line":169,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.115967Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ppm.json","function":"load_data","line":171,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.121951Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.121951Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.129929Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.129929Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4A', 'Name': 'CTG MACHINE', 'MODEL': 'F3', 'SERIAL': '560034-EM18A05070007', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2456', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '27/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '25/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '23/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4A', 'Name': 'ECG MACHINE', 'MODEL': 'SE1200EXPRESS', 'SERIAL': '460016-M19902-', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2452', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '24/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '20/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '19/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.130926Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 1040 entries","function":"load_data","line":189,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.131924Z","level":"DEBUG","module":"app.services.history_service","message":"Loaded 2 history entries","function":"_load_history_data","line":46,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.133919Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ocm'","function":"load_data","line":152,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.133919Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.133919Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.133919Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.134917Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.134917Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.135913Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.135913Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.135913Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.135913Z","level":"DEBUG","module":"app.services.data_service","message":"Selected OCM file path: data\\ocm.json","function":"load_data","line":164,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.135913Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ocm.json","function":"load_data","line":169,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.136911Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ocm.json","function":"load_data","line":171,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.138905Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.138905Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.184783Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.184783Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SW3266-', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2394', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/10/2025', 'Engineer': 'NIXON', 'Next_Maintenance': '01/10/2026', 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SX2315', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2397', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/11/2025', 'Engineer': 'MANU', 'Next_Maintenance': '01/11/2026', 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.184783Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 612 entries","function":"load_data","line":189,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.185781Z","level":"DEBUG","module":"app.services.history_service","message":"Loaded 2 history entries","function":"_load_history_data","line":46,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.301471Z","level":"INFO","module":"app","message":"Quarter summary generated successfully: {'current_date': '2025-07-20', 'current_calendar_quarter': 3, 'current_quarter_name': 'Q3', 'quarter_keys': ['PPM_Q_I', 'PPM_Q_II', 'PPM_Q_III', 'PPM_Q_IV'], 'quarter_names': ['Q1', 'Q2', 'Q3', 'Q4']}","function":"index","line":136,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.404196Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'equipment_ppm_import_export': True","function":"has_permission","line":72,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.405193Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'training_manage': True","function":"has_permission","line":72,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.405193Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'audit_log_view': True","function":"has_permission","line":72,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.405193Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'settings_manage': True","function":"has_permission","line":72,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.406190Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'user_manage': True","function":"has_permission","line":72,"thread":18372,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/auth/login?next=http://localhost:5001/"}}
{"timestamp":"2025-07-19T21:50:00.646549Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:00] \"GET / HTTP/1.1\" 200 -","function":"_log","line":97,"thread":18372,"process":12836}
{"timestamp":"2025-07-19T21:50:01.030514Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/css/modern-datepicker.css HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":6784,"process":12836}
{"timestamp":"2025-07-19T21:50:01.032527Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/css/mobile.css HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":13368,"process":12836}
{"timestamp":"2025-07-19T21:50:01.335719Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/js/modern-datepicker.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":8976,"process":12836}
{"timestamp":"2025-07-19T21:50:01.344693Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/js/department-sync.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":18996,"process":12836}
{"timestamp":"2025-07-19T21:50:01.355666Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/js/error_logger.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":12812,"process":12836}
{"timestamp":"2025-07-19T21:50:01.680979Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/js/equipment_list.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":18032,"process":12836}
{"timestamp":"2025-07-19T21:50:01.682987Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/js/main.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":14284,"process":12836}
{"timestamp":"2025-07-19T21:50:01.706924Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:01] \"\u001b[36mGET /static/js/mobile.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":12856,"process":12836}
{"timestamp":"2025-07-19T21:50:02.039037Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:50:02] \"\u001b[36mGET /static/js/notifications.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":7532,"process":12836}
{"timestamp":"2025-07-19T21:51:11.585746Z","level":"DEBUG","module":"app","message":"[app.decorators] Checking permission: ['dashboard_view']","function":"decorated_function","line":24,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.586743Z","level":"DEBUG","module":"app","message":"[app.models.json_user] Loaded permissions for Admin: ['dashboard_view', 'equipment_ppm_read', 'equipment_ppm_write', 'equipment_ppm_delete', 'equipment_ppm_import_export', 'equipment_ocm_read', 'equipment_ocm_write', 'equipment_ocm_delete', 'equipment_ocm_import_export', 'training_manage', 'training_read', 'training_write', 'training_delete', 'audit_log_view', 'audit_log_export', 'user_manage', 'settings_read', 'settings_manage', 'settings_email_test', 'backup_manage', 'import_data', 'export_data']","function":"_load_permissions","line":43,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.586743Z","level":"DEBUG","module":"app","message":"[app.decorators] Permission check result for ['dashboard_view']: True","function":"decorated_function","line":52,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.586743Z","level":"DEBUG","module":"app","message":"[app.decorators] Permission granted","function":"decorated_function","line":63,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.586743Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ppm'","function":"load_data","line":152,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.586743Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.587740Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.587740Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.588737Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.588737Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.588737Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.589735Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.589735Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.589735Z","level":"DEBUG","module":"app.services.data_service","message":"Selected PPM file path: data\\ppm.json","function":"load_data","line":161,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.589735Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ppm.json","function":"load_data","line":169,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.589735Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ppm.json","function":"load_data","line":171,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.595718Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.595718Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.602700Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.602700Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4A', 'Name': 'CTG MACHINE', 'MODEL': 'F3', 'SERIAL': '560034-EM18A05070007', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2456', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '27/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '25/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '23/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4A', 'Name': 'ECG MACHINE', 'MODEL': 'SE1200EXPRESS', 'SERIAL': '460016-M19902-', 'MANUFACTURER': 'EDAN', 'LOG_Number': '2452', 'Installation_Date': None, 'Warranty_End': None, 'PPM_Q_I': {'engineer': 'NIXON', 'quarter_date': '24/03/2025', 'status': None}, 'PPM_Q_II': {'engineer': 'JAYAPRAKASH', 'quarter_date': '22/06/2025', 'status': None}, 'PPM_Q_III': {'engineer': 'NIXON', 'quarter_date': '20/09/2025', 'status': None}, 'PPM_Q_IV': {'engineer': 'JAYAPRAKASH', 'quarter_date': '19/12/2025', 'status': None}, 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.603697Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 1040 entries","function":"load_data","line":189,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.603697Z","level":"DEBUG","module":"app.services.history_service","message":"Loaded 2 history entries","function":"_load_history_data","line":46,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.604695Z","level":"DEBUG","module":"app.services.data_service","message":"Starting load_data method with data_type='ocm'","function":"load_data","line":152,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.604695Z","level":"DEBUG","module":"app.services.data_service","message":"Calling DataService.ensure_data_files_exist()","function":"load_data","line":155,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.605692Z","level":"DEBUG","module":"app.services.data_service","message":"Ensuring data files exist","function":"ensure_data_files_exist","line":30,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.605692Z","level":"DEBUG","module":"app.services.data_service","message":"Using data directory: data","function":"ensure_data_files_exist","line":32,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.605692Z","level":"DEBUG","module":"app.services.data_service","message":"Checking PPM data file: data\\ppm.json","function":"ensure_data_files_exist","line":38,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.605692Z","level":"DEBUG","module":"app.services.data_service","message":"Checking OCM data file: data\\ocm.json","function":"ensure_data_files_exist","line":44,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.606689Z","level":"DEBUG","module":"app.services.data_service","message":"Checking existence of settings file: data\\settings.json","function":"ensure_settings_file_exists","line":56,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.606689Z","level":"DEBUG","module":"app.services.data_service","message":"Settings file data\\settings.json already exists.","function":"ensure_settings_file_exists","line":76,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.606689Z","level":"DEBUG","module":"app.services.data_service","message":"Determining file path based on data_type","function":"load_data","line":158,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.606689Z","level":"DEBUG","module":"app.services.data_service","message":"Selected OCM file path: data\\ocm.json","function":"load_data","line":164,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.607687Z","level":"DEBUG","module":"app.services.data_service","message":"Opening file for reading: data\\ocm.json","function":"load_data","line":169,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.607687Z","level":"DEBUG","module":"app.services.data_service","message":"Reading content from file: data\\ocm.json","function":"load_data","line":171,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.609681Z","level":"DEBUG","module":"app.services.data_service","message":"Checking if file content is empty","function":"load_data","line":174,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.609681Z","level":"DEBUG","module":"app.services.data_service","message":"Attempting to decode JSON content","function":"load_data","line":179,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.611676Z","level":"DEBUG","module":"app.services.data_service","message":"Successfully decoded JSON. Data type: <class 'list'>","function":"load_data","line":186,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.612673Z","level":"DEBUG","module":"app.services.data_service","message":"Loaded data sample (first 2 items): [{'NO': 1, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SW3266-', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2394', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/10/2025', 'Engineer': 'NIXON', 'Next_Maintenance': '01/10/2026', 'Status': 'Upcoming', 'has_history': False}, {'NO': 2, 'Department': '4B', 'Name': 'BP APPARATUS', 'Model': 'STANDBY', 'Serial': 'SX2315', 'Manufacturer': 'BAUMANOMETER', 'Log_Number': '2397', 'Installation_Date': 'N/A', 'Warranty_End': 'N/A', 'Service_Date': '01/11/2025', 'Engineer': 'MANU', 'Next_Maintenance': '01/11/2026', 'Status': 'Upcoming', 'has_history': False}]","function":"load_data","line":187,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.612673Z","level":"DEBUG","module":"app.services.data_service","message":"Returning loaded data with 612 entries","function":"load_data","line":189,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.612673Z","level":"DEBUG","module":"app.services.history_service","message":"Loaded 2 history entries","function":"_load_history_data","line":46,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.728365Z","level":"INFO","module":"app","message":"Quarter summary generated successfully: {'current_date': '2025-07-20', 'current_calendar_quarter': 3, 'current_quarter_name': 'Q3', 'quarter_keys': ['PPM_Q_I', 'PPM_Q_II', 'PPM_Q_III', 'PPM_Q_IV'], 'quarter_names': ['Q1', 'Q2', 'Q3', 'Q4']}","function":"index","line":136,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.729364Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'equipment_ppm_import_export': True","function":"has_permission","line":72,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.730360Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'training_manage': True","function":"has_permission","line":72,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.730360Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'audit_log_view': True","function":"has_permission","line":72,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.730360Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'settings_manage': True","function":"has_permission","line":72,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.730360Z","level":"DEBUG","module":"app","message":"[has_permission] User admin permission 'user_manage': True","function":"has_permission","line":72,"thread":19184,"process":12836,"request":{"method":"GET","url":"http://localhost:5001/","endpoint":"views.index","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":null}}
{"timestamp":"2025-07-19T21:51:11.852035Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:11] \"GET / HTTP/1.1\" 200 -","function":"_log","line":97,"thread":19184,"process":12836}
{"timestamp":"2025-07-19T21:51:11.896916Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:11] \"\u001b[36mGET /static/css/modern-datepicker.css HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":19768,"process":12836}
{"timestamp":"2025-07-19T21:51:12.192125Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/css/mobile.css HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":7772,"process":12836}
{"timestamp":"2025-07-19T21:51:12.345031Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/department-sync.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":16156,"process":12836}
{"timestamp":"2025-07-19T21:51:12.347039Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/modern-datepicker.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":1652,"process":12836}
{"timestamp":"2025-07-19T21:51:12.509603Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/error_logger.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":11440,"process":12836}
{"timestamp":"2025-07-19T21:51:12.672168Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/main.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":4132,"process":12836}
{"timestamp":"2025-07-19T21:51:12.673168Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/equipment_list.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":6368,"process":12836}
{"timestamp":"2025-07-19T21:51:12.829749Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/mobile.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":19244,"process":12836}
{"timestamp":"2025-07-19T21:51:12.996302Z","level":"INFO","module":"werkzeug","message":"127.0.0.1 - - [20/Jul/2025 00:51:12] \"\u001b[36mGET /static/js/notifications.js HTTP/1.1\u001b[0m\" 304 -","function":"_log","line":97,"thread":4844,"process":12836}
