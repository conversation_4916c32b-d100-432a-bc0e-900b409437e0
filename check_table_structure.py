#!/usr/bin/env python3
"""
Check existing table structures in Supabase
"""

import os
import sys
import logging

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_tables():
    """Check existing table structures."""
    try:
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.info("Supabase client initialized")
        
        # Check audit_logs table structure
        try:
            result = supabase.table('audit_logs').select('*').limit(1).execute()
            logger.info(f"Audit logs table exists with data: {result.data}")
            
            # Try to insert a test record to see what columns are expected
            test_record = {
                'event_type': 'Test',
                'performed_by': 'System',
                'description': 'Test description',
                'status': 'Success'
            }
            
            try:
                insert_result = supabase.table('audit_logs').insert(test_record).execute()
                logger.info("Test insert successful")
                # Delete the test record
                if insert_result.data:
                    delete_result = supabase.table('audit_logs').delete().eq('event_type', 'Test').execute()
                    logger.info("Test record deleted")
            except Exception as e:
                logger.error(f"Test insert failed: {e}")
                
        except Exception as e:
            logger.error(f"Audit logs table error: {e}")
        
        # Check push_subscriptions table structure
        try:
            result = supabase.table('push_subscriptions').select('*').limit(1).execute()
            logger.info(f"Push subscriptions table exists with data: {result.data}")
            
            # Try to insert a test record
            test_record = {
                'endpoint': 'https://test.example.com',
                'p256dh_key': 'test_key',
                'auth_key': 'test_auth'
            }
            
            try:
                insert_result = supabase.table('push_subscriptions').insert(test_record).execute()
                logger.info("Push subscriptions test insert successful")
                # Delete the test record
                if insert_result.data:
                    delete_result = supabase.table('push_subscriptions').delete().eq('endpoint', 'https://test.example.com').execute()
                    logger.info("Push subscriptions test record deleted")
            except Exception as e:
                logger.error(f"Push subscriptions test insert failed: {e}")
                
        except Exception as e:
            logger.error(f"Push subscriptions table error: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking tables: {e}")
        return False

if __name__ == "__main__":
    check_tables()
