"""
Base service class for ALORF BIOMED System.
Provides common functionality for both JSON file and database operations.
"""
import os
import json
import logging
from typing import List, Dict, Any, Optional, Union
from abc import ABC, abstractmethod
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

class BaseService(ABC):
    """Abstract base class for data services."""
    
    def __init__(self, json_file_path: str = None, table_name: str = None):
        self.json_file_path = json_file_path
        self.table_name = table_name
        self.use_database = self._should_use_database()
        
        if self.use_database:
            self._init_database()
        else:
            self._ensure_json_file_exists()
    
    def _should_use_database(self) -> bool:
        """Check if we should use database instead of JSON files."""
        return os.getenv('USE_SUPABASE', 'false').lower() == 'true'
    
    def _init_database(self):
        """Initialize database connection."""
        try:
            from app.database import get_db_engine, get_db_session
            self.engine = get_db_engine()
            self.get_session = get_db_session
            logger.debug(f"Database initialized for {self.table_name}")
        except Exception as e:
            logger.error(f"Failed to initialize database for {self.table_name}: {e}")
            # Fallback to JSON files
            self.use_database = False
            self._ensure_json_file_exists()
    
    def _ensure_json_file_exists(self):
        """Ensure JSON file exists."""
        if self.json_file_path and not os.path.exists(self.json_file_path):
            os.makedirs(os.path.dirname(self.json_file_path), exist_ok=True)
            with open(self.json_file_path, 'w', encoding='utf-8') as f:
                json.dump([], f, indent=2)
            logger.info(f"Created new JSON file: {self.json_file_path}")
    
    # JSON File Operations
    def _load_json_data(self) -> List[Dict[str, Any]]:
        """Load data from JSON file."""
        try:
            if not os.path.exists(self.json_file_path):
                return []
            
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data if isinstance(data, list) else []
            
        except Exception as e:
            logger.error(f"Failed to load JSON data from {self.json_file_path}: {e}")
            return []
    
    def _save_json_data(self, data: List[Dict[str, Any]]) -> bool:
        """Save data to JSON file."""
        try:
            with open(self.json_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Failed to save JSON data to {self.json_file_path}: {e}")
            return False
    
    # Database Operations
    def _execute_query(self, query: str, params: Dict[str, Any] = None) -> Any:
        """Execute a database query."""
        try:
            with self.engine.connect() as conn:
                if params:
                    result = conn.execute(text(query), params)
                else:
                    result = conn.execute(text(query))
                conn.commit()
                return result
        except SQLAlchemyError as e:
            logger.error(f"Database query failed: {e}")
            raise
    
    def _fetch_all(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Fetch all results from a query."""
        try:
            result = self._execute_query(query, params)
            columns = result.keys()
            return [dict(zip(columns, row)) for row in result.fetchall()]
        except Exception as e:
            logger.error(f"Failed to fetch data: {e}")
            return []
    
    def _fetch_one(self, query: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Fetch one result from a query."""
        try:
            result = self._execute_query(query, params)
            row = result.fetchone()
            if row:
                columns = result.keys()
                return dict(zip(columns, row))
            return None
        except Exception as e:
            logger.error(f"Failed to fetch single record: {e}")
            return None
    
    # Abstract methods that must be implemented by subclasses
    @abstractmethod
    def get_all(self) -> List[Dict[str, Any]]:
        """Get all records."""
        pass
    
    @abstractmethod
    def get_by_id(self, record_id: Union[int, str]) -> Optional[Dict[str, Any]]:
        """Get a record by ID."""
        pass
    
    @abstractmethod
    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new record."""
        pass
    
    @abstractmethod
    def update(self, record_id: Union[int, str], data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an existing record."""
        pass
    
    @abstractmethod
    def delete(self, record_id: Union[int, str]) -> bool:
        """Delete a record."""
        pass
    
    # Utility methods
    def _generate_id(self, data: List[Dict[str, Any]]) -> int:
        """Generate a new ID for JSON data."""
        if not data:
            return 1
        return max(item.get('id', 0) for item in data) + 1
    
    def _add_timestamps(self, data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """Add created_at and updated_at timestamps."""
        now = datetime.now().isoformat()
        
        if not is_update:
            data['created_at'] = now
        data['updated_at'] = now
        
        return data
    
    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> bool:
        """Validate that required fields are present."""
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            logger.error(f"Missing required fields: {missing_fields}")
            return False
        return True
    
    def count(self) -> int:
        """Get total count of records."""
        if self.use_database:
            try:
                result = self._execute_query(f"SELECT COUNT(*) FROM {self.table_name}")
                return result.fetchone()[0]
            except Exception as e:
                logger.error(f"Failed to count records in {self.table_name}: {e}")
                return 0
        else:
            return len(self._load_json_data())
    
    def search(self, field: str, value: str) -> List[Dict[str, Any]]:
        """Search records by field value."""
        if self.use_database:
            query = f"SELECT * FROM {self.table_name} WHERE {field} ILIKE :value"
            return self._fetch_all(query, {'value': f'%{value}%'})
        else:
            data = self._load_json_data()
            return [item for item in data if value.lower() in str(item.get(field, '')).lower()]
    
    def filter_by(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter records by multiple criteria."""
        if self.use_database:
            conditions = []
            params = {}
            for key, value in filters.items():
                conditions.append(f"{key} = :{key}")
                params[key] = value
            
            query = f"SELECT * FROM {self.table_name} WHERE {' AND '.join(conditions)}"
            return self._fetch_all(query, params)
        else:
            data = self._load_json_data()
            filtered_data = data
            for key, value in filters.items():
                filtered_data = [item for item in filtered_data if item.get(key) == value]
            return filtered_data
    
    def backup_data(self) -> bool:
        """Create a backup of current data."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if self.use_database:
                # Export database data to JSON backup
                data = self.get_all()
                backup_path = f"data/backups/{self.table_name}_backup_{timestamp}.json"
            else:
                # Copy JSON file
                data = self._load_json_data()
                backup_path = f"data/backups/{os.path.basename(self.json_file_path)}_backup_{timestamp}.json"
            
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Backup created: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return False
