"""
Supabase-based Audit Service
Replaces JSON file-based audit logging with Supabase database storage
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from supabase import create_client, Client

from app.config import Config

logger = logging.getLogger(__name__)

class SupabaseAuditService:
    """Service for managing audit logs using Supabase database."""
    
    # Event types (same as original AuditService)
    EVENT_TYPES = {
        'SYSTEM_STARTUP': 'System Startup',
        'EQUIPMENT_ADDED': 'Equipment Added',
        'EQUIPMENT_UPDATED': 'Equipment Updated',
        'EQUIPMENT_DELETED': 'Equipment Deleted',
        'REMINDER_SENT': 'Reminder Sent',
        'BULK_IMPORT': 'Bulk Import',
        'BULK_EXPORT': 'Bulk Export',
        'SETTING_CHANGED': 'Setting Changed',
        'USER_ACTION': 'User Action',
        'BACKUP_CREATED': 'Backup Created',
        '<PERSON>C<PERSON>UP_RESTORED': 'Backup Restored',
        'BACKUP_DELETED': 'Backup Deleted',
        'DATA_EXPORT': 'Data Export',
        'TEST_EMAIL': 'Test Email'
    }
    
    # Status types
    STATUS_SUCCESS = 'Success'
    STATUS_FAILED = 'Failed'
    STATUS_WARNING = 'Warning'
    STATUS_INFO = 'Info'
    
    def __init__(self):
        """Initialize the Supabase client."""
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.debug("SupabaseAuditService initialized")

    def log_event(
        self,
        event_type: str,
        performed_by: str,
        description: str,
        status: str = STATUS_SUCCESS,
        details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Log a new audit event to Supabase.
        
        Args:
            event_type: Type of event (use EVENT_TYPES constants)
            performed_by: User or system that performed the action
            description: Human-readable description of the action
            status: Status of the action (Success, Failed, Warning, Info)
            details: Additional details as a dictionary
            
        Returns:
            bool: True if logged successfully, False otherwise
        """
        try:
            record = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'performed_by': performed_by,
                'description': description,
                'status': status,
                'details': details or {}
            }
            
            result = self.supabase.table('audit_logs').insert(record).execute()
            logger.debug(f"Audit event logged: {event_type} by {performed_by}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return False

    def get_all_logs(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get all audit logs, ordered by timestamp descending."""
        try:
            result = self.supabase.table('audit_logs').select('*').order('timestamp', desc=True).limit(limit).execute()
            
            logs = []
            for log in result.data:
                # Convert to format expected by existing code
                log_entry = {
                    'id': log.get('id'),
                    'timestamp': log.get('timestamp', ''),
                    'event_type': log.get('event_type', ''),
                    'performed_by': log.get('performed_by', ''),
                    'description': log.get('description', ''),
                    'status': log.get('status', 'Success'),
                    'details': log.get('details', {})
                }
                logs.append(log_entry)
            
            logger.info(f"Retrieved {len(logs)} audit logs from database")
            return logs
            
        except Exception as e:
            logger.error(f"Failed to get audit logs: {e}")
            return []

    def get_logs_by_date_range(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get audit logs within a date range."""
        try:
            result = self.supabase.table('audit_logs').select('*').gte('timestamp', start_date).lte('timestamp', end_date).order('timestamp', desc=True).execute()
            
            logs = []
            for log in result.data:
                log_entry = {
                    'id': log.get('id'),
                    'timestamp': log.get('timestamp', ''),
                    'event_type': log.get('event_type', ''),
                    'performed_by': log.get('performed_by', ''),
                    'description': log.get('description', ''),
                    'status': log.get('status', 'Success'),
                    'details': log.get('details', {})
                }
                logs.append(log_entry)
            
            logger.info(f"Retrieved {len(logs)} audit logs for date range {start_date} to {end_date}")
            return logs
            
        except Exception as e:
            logger.error(f"Failed to get audit logs by date range: {e}")
            return []

    def get_logs_by_event_type(self, event_type: str) -> List[Dict[str, Any]]:
        """Get audit logs by event type."""
        try:
            result = self.supabase.table('audit_logs').select('*').eq('event_type', event_type).order('timestamp', desc=True).execute()
            
            logs = []
            for log in result.data:
                log_entry = {
                    'id': log.get('id'),
                    'timestamp': log.get('timestamp', ''),
                    'event_type': log.get('event_type', ''),
                    'performed_by': log.get('performed_by', ''),
                    'description': log.get('description', ''),
                    'status': log.get('status', 'Success'),
                    'details': log.get('details', {})
                }
                logs.append(log_entry)
            
            logger.info(f"Retrieved {len(logs)} audit logs for event type: {event_type}")
            return logs
            
        except Exception as e:
            logger.error(f"Failed to get audit logs by event type: {e}")
            return []

    def get_logs_by_user(self, user: str) -> List[Dict[str, Any]]:
        """Get audit logs by user."""
        try:
            result = self.supabase.table('audit_logs').select('*').eq('performed_by', user).order('timestamp', desc=True).execute()
            
            logs = []
            for log in result.data:
                log_entry = {
                    'id': log.get('id'),
                    'timestamp': log.get('timestamp', ''),
                    'event_type': log.get('event_type', ''),
                    'performed_by': log.get('performed_by', ''),
                    'description': log.get('description', ''),
                    'status': log.get('status', 'Success'),
                    'details': log.get('details', {})
                }
                logs.append(log_entry)
            
            logger.info(f"Retrieved {len(logs)} audit logs for user: {user}")
            return logs
            
        except Exception as e:
            logger.error(f"Failed to get audit logs by user: {e}")
            return []

    def search_logs(self, query: str) -> List[Dict[str, Any]]:
        """Search audit logs by description, event type, or user."""
        try:
            # Use ilike for case-insensitive search
            result = self.supabase.table('audit_logs').select('*').or_(
                f'description.ilike.%{query}%,event_type.ilike.%{query}%,performed_by.ilike.%{query}%'
            ).order('timestamp', desc=True).execute()
            
            logs = []
            for log in result.data:
                log_entry = {
                    'id': log.get('id'),
                    'timestamp': log.get('timestamp', ''),
                    'event_type': log.get('event_type', ''),
                    'performed_by': log.get('performed_by', ''),
                    'description': log.get('description', ''),
                    'status': log.get('status', 'Success'),
                    'details': log.get('details', {})
                }
                logs.append(log_entry)
            
            logger.info(f"Found {len(logs)} audit logs matching query: {query}")
            return logs
            
        except Exception as e:
            logger.error(f"Failed to search audit logs: {e}")
            return []

    def get_event_types(self) -> List[str]:
        """Get all available event types."""
        return list(self.EVENT_TYPES.values())

    def get_stats(self) -> Dict[str, Any]:
        """Get audit log statistics."""
        try:
            # Get total count
            total_result = self.supabase.table('audit_logs').select('*', count='exact').execute()
            total_count = total_result.count
            
            # Get counts by status
            success_result = self.supabase.table('audit_logs').select('*', count='exact').eq('status', 'Success').execute()
            failed_result = self.supabase.table('audit_logs').select('*', count='exact').eq('status', 'Failed').execute()
            
            stats = {
                'total_logs': total_count,
                'success_count': success_result.count,
                'failed_count': failed_result.count,
                'warning_count': 0,  # Can add this if needed
                'info_count': 0     # Can add this if needed
            }
            
            logger.info(f"Retrieved audit log stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get audit log stats: {e}")
            return {'total_logs': 0, 'success_count': 0, 'failed_count': 0, 'warning_count': 0, 'info_count': 0}

    def clear_logs(self) -> bool:
        """Clear all audit logs (admin only)."""
        try:
            result = self.supabase.table('audit_logs').delete().neq('id', 0).execute()  # Delete all records
            logger.warning("All audit logs cleared from database")
            return True
        except Exception as e:
            logger.error(f"Failed to clear audit logs: {e}")
            return False

    def migrate_from_json(self, json_logs: List[Dict[str, Any]]) -> bool:
        """Migrate audit logs from JSON format to Supabase."""
        try:
            logger.info(f"Migrating {len(json_logs)} audit logs from JSON to Supabase")
            
            # Migrate in batches to avoid memory issues
            batch_size = 50
            total_batches = (len(json_logs) + batch_size - 1) // batch_size
            
            for i in range(0, len(json_logs), batch_size):
                batch = json_logs[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                logger.info(f"Migrating batch {batch_num}/{total_batches} ({len(batch)} entries)")
                
                # Prepare batch data
                batch_records = []
                for log_entry in batch:
                    # Convert timestamp to proper format
                    timestamp_str = log_entry.get('timestamp', '')
                    try:
                        # Parse the timestamp and convert to ISO format
                        dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        iso_timestamp = dt.isoformat()
                    except:
                        iso_timestamp = datetime.now().isoformat()
                    
                    record = {
                        'timestamp': iso_timestamp,
                        'event_type': log_entry.get('event_type', 'Unknown'),
                        'performed_by': log_entry.get('performed_by', 'System'),
                        'description': log_entry.get('description', ''),
                        'status': log_entry.get('status', 'Success'),
                        'details': log_entry.get('details', {})
                    }
                    batch_records.append(record)
                
                # Insert batch
                result = self.supabase.table('audit_logs').insert(batch_records).execute()
                logger.info(f"Batch {batch_num} migrated successfully")
            
            logger.info("Audit logs migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate audit logs from JSON: {e}")
            return False


# Global instance for backward compatibility
_audit_service = None

def get_audit_service() -> SupabaseAuditService:
    """Get the global audit service instance."""
    global _audit_service
    if _audit_service is None:
        _audit_service = SupabaseAuditService()
    return _audit_service
