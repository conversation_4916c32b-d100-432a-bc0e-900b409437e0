#!/usr/bin/env python3
"""
Simple test script for TrainerService to verify JSON mode works.
"""
import os
import sys
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_json_mode():
    """Test JSON file mode."""
    logger.info("🧪 Testing TrainerService JSON File Mode")
    
    # Set environment for JSON mode
    os.environ['USE_SUPABASE'] = 'false'
    
    try:
        from app.services.trainer_service import TrainerService
        
        service = TrainerService()
        logger.info(f"✅ Service initialized (database mode: {service.use_database})")
        
        # Test create (without department for simplicity)
        trainer_data = {
            'name': 'Test Trainer Simple',
            'department_id': None,
            'telephone': '************',
            'information': 'Test trainer info'
        }
        created = service.create(trainer_data)
        assert created is not None
        logger.info(f"✅ Created trainer: {created['name']}")
        
        # Test get all
        all_trainers = service.get_all()
        assert len(all_trainers) > 0
        logger.info(f"✅ Found {len(all_trainers)} trainers")
        
        # Test get by ID
        found = service.get_by_id(created['id'])
        assert found is not None
        assert found['name'] == 'Test Trainer Simple'
        logger.info(f"✅ Found trainer by ID: {found['name']}")
        
        # Test update
        updated = service.update(created['id'], {
            'name': 'Updated Test Trainer Simple',
            'telephone': '************'
        })
        assert updated is not None
        assert updated['name'] == 'Updated Test Trainer Simple'
        assert updated['telephone'] == '************'
        logger.info(f"✅ Updated trainer: {updated['name']}")
        
        # Test delete
        deleted = service.delete(created['id'])
        assert deleted is True
        logger.info("✅ Deleted trainer")
        
        logger.info("🎉 JSON File Mode: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ JSON File Mode failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_legacy_methods():
    """Test legacy static methods."""
    logger.info("🧪 Testing Legacy Methods")
    
    # Set environment for JSON mode
    os.environ['USE_SUPABASE'] = 'false'
    
    try:
        from app.services.trainer_service import TrainerService
        
        # Test legacy static methods
        all_trainers = TrainerService.get_all_trainers()
        logger.info(f"✅ Legacy get_all_trainers: {len(all_trainers)} trainers")
        
        dropdown_trainers = TrainerService.get_trainers_for_dropdown()
        logger.info(f"✅ Legacy get_trainers_for_dropdown: {len(dropdown_trainers)} options")
        
        trainers_with_dept = TrainerService.get_trainers_with_department_info()
        logger.info(f"✅ Legacy get_trainers_with_department_info: {len(trainers_with_dept)} trainers")
        
        logger.info("🎉 Legacy Methods: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Legacy Methods failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("🚀 TrainerService Simple Test Suite")
    logger.info("=" * 50)
    
    # Test JSON mode
    json_success = test_json_mode()
    
    print()  # Add spacing
    
    # Test legacy methods
    legacy_success = test_legacy_methods()
    
    # Summary
    logger.info("=" * 50)
    logger.info("📊 FINAL RESULTS")
    logger.info(f"JSON File Mode: {'✅ PASSED' if json_success else '❌ FAILED'}")
    logger.info(f"Legacy Methods: {'✅ PASSED' if legacy_success else '❌ FAILED'}")
    
    overall_success = json_success and legacy_success
    if overall_success:
        logger.info("🎉 All tests passed! TrainerService is ready.")
    else:
        logger.error("❌ Some tests failed.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
