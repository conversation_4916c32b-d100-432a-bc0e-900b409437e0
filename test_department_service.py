#!/usr/bin/env python3
"""
Comprehensive test script for DepartmentService.
Tests both JSON file and database modes.
"""
import os
import sys
import json
import logging
import tempfile
import shutil
from datetime import datetime
from typing import Dict, Any, List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DepartmentServiceTester:
    """Test class for DepartmentService."""
    
    def __init__(self):
        self.test_results = []
        self.original_env = {}
        self.temp_dir = None
        self.original_data_dir = None
    
    def setup_test_environment(self, use_database: bool = False):
        """Setup test environment."""
        logger.info(f"Setting up test environment (database: {use_database})")
        
        # Save original environment
        self.original_env = {
            'USE_SUPABASE': os.getenv('USE_SUPABASE', 'false'),
            'DATABASE_URL': os.getenv('DATABASE_URL', ''),
        }
        
        if use_database:
            # Set up database mode
            os.environ['USE_SUPABASE'] = 'true'
            if not os.getenv('DATABASE_URL'):
                logger.error("DATABASE_URL not set for database testing")
                return False
        else:
            # Set up JSON file mode
            os.environ['USE_SUPABASE'] = 'false'
            
            # Create temporary directory for test data
            self.temp_dir = tempfile.mkdtemp()
            self.original_data_dir = os.getcwd()
            
            # Create test data directory
            test_data_dir = os.path.join(self.temp_dir, 'data')
            os.makedirs(test_data_dir, exist_ok=True)
            
            # Create empty departments.json
            departments_file = os.path.join(test_data_dir, 'departments.json')
            with open(departments_file, 'w') as f:
                json.dump([], f)
            
            # Update the data directory path
            os.chdir(self.temp_dir)
        
        return True
    
    def cleanup_test_environment(self):
        """Cleanup test environment."""
        logger.info("Cleaning up test environment")
        
        # Restore original environment
        for key, value in self.original_env.items():
            if value:
                os.environ[key] = value
            elif key in os.environ:
                del os.environ[key]
        
        # Cleanup temporary directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        if self.original_data_dir:
            os.chdir(self.original_data_dir)
    
    def run_test(self, test_name: str, test_func) -> bool:
        """Run a single test and record results."""
        logger.info(f"Running test: {test_name}")
        try:
            result = test_func()
            if result:
                logger.info(f"✅ {test_name} PASSED")
                self.test_results.append((test_name, True, None))
                return True
            else:
                logger.error(f"❌ {test_name} FAILED")
                self.test_results.append((test_name, False, "Test returned False"))
                return False
        except Exception as e:
            logger.error(f"❌ {test_name} CRASHED: {e}")
            self.test_results.append((test_name, False, str(e)))
            return False
    
    def test_service_initialization(self) -> bool:
        """Test service initialization."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # Check if service is properly initialized
            assert hasattr(service, 'use_database')
            assert hasattr(service, 'json_file_path')
            assert hasattr(service, 'table_name')
            
            logger.info(f"Service initialized with database mode: {service.use_database}")
            return True
        except Exception as e:
            logger.error(f"Service initialization failed: {e}")
            return False
    
    def test_create_department(self) -> bool:
        """Test creating a department."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # Test data
            dept_data = {
                'department_name': 'Test Department',
                'information': 'This is a test department'
            }
            
            # Create department
            result = service.create(dept_data)
            
            # Verify result
            assert result is not None
            assert result['department_name'] == 'Test Department'
            assert result['information'] == 'This is a test department'
            assert 'id' in result
            assert 'created_date' in result
            assert 'updated_date' in result
            
            logger.info(f"Created department with ID: {result['id']}")
            return True
        except Exception as e:
            logger.error(f"Create department test failed: {e}")
            return False
    
    def test_get_all_departments(self) -> bool:
        """Test getting all departments."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # Get all departments
            departments = service.get_all()
            
            # Verify result
            assert isinstance(departments, list)
            
            # Should have at least the test department we created
            if len(departments) > 0:
                dept = departments[0]
                assert 'id' in dept
                assert 'department_name' in dept
                logger.info(f"Found {len(departments)} departments")
            
            return True
        except Exception as e:
            logger.error(f"Get all departments test failed: {e}")
            return False
    
    def test_get_department_by_id(self) -> bool:
        """Test getting department by ID."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # First create a department
            dept_data = {
                'department_name': 'Test Department 2',
                'information': 'Another test department'
            }
            created_dept = service.create(dept_data)
            assert created_dept is not None
            
            # Get department by ID
            found_dept = service.get_by_id(created_dept['id'])
            
            # Verify result
            assert found_dept is not None
            assert found_dept['id'] == created_dept['id']
            assert found_dept['department_name'] == 'Test Department 2'
            
            logger.info(f"Found department by ID: {found_dept['department_name']}")
            return True
        except Exception as e:
            logger.error(f"Get department by ID test failed: {e}")
            return False
    
    def test_update_department(self) -> bool:
        """Test updating a department."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # First create a department
            dept_data = {
                'department_name': 'Test Department 3',
                'information': 'Original information'
            }
            created_dept = service.create(dept_data)
            assert created_dept is not None
            
            # Update department
            update_data = {
                'department_name': 'Updated Test Department 3',
                'information': 'Updated information'
            }
            updated_dept = service.update(created_dept['id'], update_data)
            
            # Verify result
            assert updated_dept is not None
            assert updated_dept['id'] == created_dept['id']
            assert updated_dept['department_name'] == 'Updated Test Department 3'
            assert updated_dept['information'] == 'Updated information'
            
            logger.info(f"Updated department: {updated_dept['department_name']}")
            return True
        except Exception as e:
            logger.error(f"Update department test failed: {e}")
            return False
    
    def test_delete_department(self) -> bool:
        """Test deleting a department."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # First create a department
            dept_data = {
                'department_name': 'Test Department to Delete',
                'information': 'This will be deleted'
            }
            created_dept = service.create(dept_data)
            assert created_dept is not None
            
            # Delete department
            result = service.delete(created_dept['id'])
            
            # Verify result
            assert result is True
            
            # Verify department is gone
            found_dept = service.get_by_id(created_dept['id'])
            assert found_dept is None
            
            logger.info(f"Deleted department with ID: {created_dept['id']}")
            return True
        except Exception as e:
            logger.error(f"Delete department test failed: {e}")
            return False
    
    def test_duplicate_name_prevention(self) -> bool:
        """Test that duplicate department names are prevented."""
        try:
            from app.services.department_service import DepartmentService
            service = DepartmentService()
            
            # Create first department
            dept_data = {
                'department_name': 'Unique Department',
                'information': 'First department'
            }
            first_dept = service.create(dept_data)
            assert first_dept is not None
            
            # Try to create second department with same name
            duplicate_data = {
                'department_name': 'Unique Department',
                'information': 'Duplicate department'
            }
            duplicate_dept = service.create(duplicate_data)
            
            # Should return None (creation failed)
            assert duplicate_dept is None
            
            logger.info("Duplicate name prevention working correctly")
            return True
        except Exception as e:
            logger.error(f"Duplicate name prevention test failed: {e}")
            return False
    
    def run_all_tests(self, use_database: bool = False) -> bool:
        """Run all tests for the specified mode."""
        mode_name = "Database" if use_database else "JSON File"
        logger.info(f"🚀 Running DepartmentService tests in {mode_name} mode")
        logger.info("=" * 60)
        
        # Setup test environment
        if not self.setup_test_environment(use_database):
            logger.error("Failed to setup test environment")
            return False
        
        try:
            # Define tests
            tests = [
                ("Service Initialization", self.test_service_initialization),
                ("Create Department", self.test_create_department),
                ("Get All Departments", self.test_get_all_departments),
                ("Get Department by ID", self.test_get_department_by_id),
                ("Update Department", self.test_update_department),
                ("Delete Department", self.test_delete_department),
                ("Duplicate Name Prevention", self.test_duplicate_name_prevention),
            ]
            
            # Run tests
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                if self.run_test(test_name, test_func):
                    passed += 1
            
            # Print summary
            logger.info("=" * 60)
            logger.info(f"📊 {mode_name} Mode Test Results: {passed}/{total} passed")
            
            if passed == total:
                logger.info(f"🎉 All {mode_name} mode tests passed!")
                return True
            else:
                logger.error(f"❌ {total - passed} {mode_name} mode tests failed")
                return False
                
        finally:
            self.cleanup_test_environment()

def main():
    """Main test function."""
    logger.info("🧪 DepartmentService Comprehensive Test Suite")
    logger.info("=" * 60)
    
    tester = DepartmentServiceTester()
    
    # Test JSON file mode
    json_success = tester.run_all_tests(use_database=False)
    
    # Reset test results for database mode
    tester.test_results = []
    
    # Test database mode (only if DATABASE_URL is set)
    database_success = True
    if os.getenv('DATABASE_URL'):
        database_success = tester.run_all_tests(use_database=True)
    else:
        logger.warning("⚠️  Skipping database tests - DATABASE_URL not set")
    
    # Overall summary
    logger.info("\n" + "=" * 60)
    logger.info("🏁 FINAL TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"JSON File Mode: {'✅ PASSED' if json_success else '❌ FAILED'}")
    logger.info(f"Database Mode: {'✅ PASSED' if database_success else '❌ FAILED'}")
    
    overall_success = json_success and database_success
    if overall_success:
        logger.info("🎉 All tests passed! DepartmentService is ready for production.")
    else:
        logger.error("❌ Some tests failed. Please fix issues before proceeding.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
