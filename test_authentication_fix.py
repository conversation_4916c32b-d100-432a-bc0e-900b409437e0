#!/usr/bin/env python3
"""
Test the fixed authentication system with Supabase
"""

import os
import sys
import logging

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_supabase_user_model():
    """Test the new SupabaseUser model."""
    try:
        logger.info("=== Testing SupabaseUser Model ===")
        
        from app.models.supabase_user import SupabaseUser
        
        # Test credentials
        test_username = "admin"
        test_password = "@Xx123456789xX@"
        
        # Test getting user
        user = SupabaseUser.get_user(test_username)
        
        if user:
            logger.info(f"✅ User found: {user.username}")
            logger.info(f"  - Role: {user.role}")
            logger.info(f"  - Active: {user.is_active}")
            
            # Test password verification
            password_valid = user.check_password(test_password)
            logger.info(f"  - Password valid: {password_valid}")
            
            if password_valid:
                logger.info("✅ SupabaseUser model working correctly!")
                return True
            else:
                logger.error("❌ Password verification failed")
                return False
        else:
            logger.error(f"❌ User '{test_username}' not found")
            return False
        
    except Exception as e:
        logger.error(f"Error testing SupabaseUser model: {e}")
        return False

def test_user_permissions():
    """Test user permissions loading."""
    try:
        logger.info("=== Testing User Permissions ===")
        
        from app.models.supabase_user import SupabaseUser
        
        # Test admin user permissions
        admin_user = SupabaseUser.get_user("admin")
        
        if admin_user:
            permissions = admin_user.permissions
            logger.info(f"Admin permissions: {permissions}")
            
            # Test a permission check
            has_admin_perm = admin_user.has_permission("admin")
            logger.info(f"Has admin permission: {has_admin_perm}")
            
            logger.info("✅ User permissions working correctly!")
            return True
        else:
            logger.error("❌ Could not load admin user for permission test")
            return False
        
    except Exception as e:
        logger.error(f"Error testing user permissions: {e}")
        return False

def test_all_users():
    """Test loading all users."""
    try:
        logger.info("=== Testing All Users Loading ===")
        
        from app.models.supabase_user import SupabaseUser
        
        users = SupabaseUser.get_all_users()
        logger.info(f"Found {len(users)} users:")
        
        for user in users:
            logger.info(f"  - {user.username} ({user.role})")
        
        if len(users) >= 4:
            logger.info("✅ All users loaded correctly!")
            return True
        else:
            logger.warning(f"⚠ Expected at least 4 users, found {len(users)}")
            return False
        
    except Exception as e:
        logger.error(f"Error testing all users: {e}")
        return False

def test_authentication_flow():
    """Test the complete authentication flow."""
    try:
        logger.info("=== Testing Complete Authentication Flow ===")
        
        # Test the authentication flow as it would work in the app
        from app.models.supabase_user import SupabaseUser
        
        # Simulate login process
        username = "admin"
        password = "@Xx123456789xX@"
        
        logger.info(f"Simulating login for: {username}")
        
        # Step 1: Get user (as done in auth route)
        user = SupabaseUser.get_user(username)
        
        if not user:
            logger.error("❌ User not found in authentication flow")
            return False
        
        logger.info(f"✅ User found: {user.username}")
        
        # Step 2: Check password (as done in auth route)
        if user.check_password(password):
            logger.info("✅ Password verification successful")
            
            # Step 3: Check user properties
            logger.info(f"  - Username: {user.username}")
            logger.info(f"  - Role: {user.role}")
            logger.info(f"  - Active: {user.is_active}")
            logger.info(f"  - User ID: {user.id}")
            
            # Step 4: Test Flask-Login compatibility
            logger.info(f"  - Is authenticated: {hasattr(user, 'is_authenticated')}")
            logger.info(f"  - Is active: {hasattr(user, 'is_active')}")
            logger.info(f"  - Is anonymous: {hasattr(user, 'is_anonymous')}")
            logger.info(f"  - Get ID: {hasattr(user, 'get_id')}")
            
            logger.info("✅ Complete authentication flow working!")
            return True
        else:
            logger.error("❌ Password verification failed in authentication flow")
            return False
        
    except Exception as e:
        logger.error(f"Error testing authentication flow: {e}")
        return False

def test_fallback_mechanism():
    """Test the fallback mechanism to JSON files."""
    try:
        logger.info("=== Testing Fallback Mechanism ===")
        
        from app.models.supabase_user import SupabaseUser
        
        # Try to get a user that might not exist in Supabase
        # This should test the fallback to JSON
        user = SupabaseUser.get_user("nonexistent_user")
        
        if user is None:
            logger.info("✅ Fallback mechanism working (user not found)")
            return True
        else:
            logger.info(f"User found: {user.username} (this is also fine)")
            return True
        
    except Exception as e:
        logger.error(f"Error testing fallback mechanism: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=== ALORF BIOMED Authentication Fix Test Started ===")
    
    tests = [
        ("SupabaseUser Model", test_supabase_user_model),
        ("User Permissions", test_user_permissions),
        ("All Users Loading", test_all_users),
        ("Authentication Flow", test_authentication_flow),
        ("Fallback Mechanism", test_fallback_mechanism)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n=== Test Results Summary ===")
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\nTotal: {len(results)} tests, {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All Authentication Tests Passed! Login should work now!")
        logger.info("\nYou can now try logging in with:")
        logger.info("  Username: admin")
        logger.info("  Password: @Xx123456789xX@")
        return True
    else:
        logger.error(f"❌ {failed} Tests Failed! Authentication issues remain.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
