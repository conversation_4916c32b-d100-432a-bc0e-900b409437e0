#!/usr/bin/env python3
"""
Simple test script for TrainerService to verify both modes work.
"""
import os
import sys
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_json_mode():
    """Test JSON file mode."""
    logger.info("🧪 Testing TrainerService JSON File Mode")
    
    # Set environment for JSON mode
    os.environ['USE_SUPABASE'] = 'false'
    
    try:
        from app.services.trainer_service import TrainerService
        from app.services.department_service import DepartmentService
        
        # First create a department for testing
        dept_service = DepartmentService()
        dept_data = {'department_name': 'Test Dept for Trainer', 'information': 'Test department'}
        test_dept = dept_service.create(dept_data)
        assert test_dept is not None
        logger.info(f"✅ Created test department: {test_dept['department_name']}")
        
        # Now test trainer service
        service = TrainerService()
        logger.info(f"✅ Service initialized (database mode: {service.use_database})")
        
        # Test create
        trainer_data = {
            'name': 'Test Trainer JSON',
            'department_id': test_dept['id'],
            'telephone': '************',
            'information': 'Test trainer info'
        }
        created = service.create(trainer_data)
        assert created is not None
        logger.info(f"✅ Created trainer: {created['name']}")
        
        # Test get all
        all_trainers = service.get_all()
        assert len(all_trainers) > 0
        logger.info(f"✅ Found {len(all_trainers)} trainers")
        
        # Test get by ID
        found = service.get_by_id(created['id'])
        assert found is not None
        assert found['name'] == 'Test Trainer JSON'
        logger.info(f"✅ Found trainer by ID: {found['name']}")
        
        # Test get by name
        found_by_name = service.get_by_name('Test Trainer JSON')
        assert found_by_name is not None
        assert found_by_name['id'] == created['id']
        logger.info(f"✅ Found trainer by name: {found_by_name['name']}")
        
        # Test get by department
        dept_trainers = service.get_trainers_by_department(test_dept['id'])
        assert len(dept_trainers) > 0
        logger.info(f"✅ Found {len(dept_trainers)} trainers in department")
        
        # Test update
        updated = service.update(created['id'], {
            'name': 'Updated Test Trainer JSON',
            'telephone': '************'
        })
        assert updated is not None
        assert updated['name'] == 'Updated Test Trainer JSON'
        assert updated['telephone'] == '************'
        logger.info(f"✅ Updated trainer: {updated['name']}")
        
        # Test delete
        deleted = service.delete(created['id'])
        assert deleted is True
        logger.info("✅ Deleted trainer")
        
        # Cleanup test department
        dept_service.delete(test_dept['id'])
        logger.info("✅ Cleaned up test department")
        
        logger.info("🎉 JSON File Mode: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ JSON File Mode failed: {e}")
        return False

def test_database_mode():
    """Test database mode."""
    logger.info("🧪 Testing TrainerService Database Mode")
    
    # Check if database is configured
    if not os.getenv('DATABASE_URL'):
        logger.warning("⚠️  Skipping database tests - DATABASE_URL not set")
        return True
    
    # Set environment for database mode
    os.environ['USE_SUPABASE'] = 'true'
    
    try:
        from app.services.trainer_service import TrainerService
        from app.services.department_service import DepartmentService
        
        # First create a department for testing
        dept_service = DepartmentService()
        dept_data = {'department_name': 'Test Dept for Trainer DB', 'information': 'Test department DB'}
        test_dept = dept_service.create(dept_data)
        assert test_dept is not None
        logger.info(f"✅ Created test department: {test_dept['department_name']}")
        
        # Now test trainer service
        service = TrainerService()
        logger.info(f"✅ Service initialized (database mode: {service.use_database})")
        
        # Test create
        trainer_data = {
            'name': 'Test Trainer DB',
            'department_id': test_dept['id'],
            'telephone': '************',
            'information': 'Test trainer info DB'
        }
        created = service.create(trainer_data)
        assert created is not None
        logger.info(f"✅ Created trainer: {created['name']}")
        
        # Test get all
        all_trainers = service.get_all()
        assert len(all_trainers) > 0
        logger.info(f"✅ Found {len(all_trainers)} trainers")
        
        # Test get by ID
        found = service.get_by_id(created['id'])
        assert found is not None
        assert found['name'] == 'Test Trainer DB'
        logger.info(f"✅ Found trainer by ID: {found['name']}")
        
        # Test get by name
        found_by_name = service.get_by_name('Test Trainer DB')
        assert found_by_name is not None
        assert found_by_name['id'] == created['id']
        logger.info(f"✅ Found trainer by name: {found_by_name['name']}")
        
        # Test get by department
        dept_trainers = service.get_trainers_by_department(test_dept['id'])
        assert len(dept_trainers) > 0
        logger.info(f"✅ Found {len(dept_trainers)} trainers in department")
        
        # Test update
        updated = service.update(created['id'], {
            'name': 'Updated Test Trainer DB',
            'telephone': '************'
        })
        assert updated is not None
        assert updated['name'] == 'Updated Test Trainer DB'
        assert updated['telephone'] == '************'
        logger.info(f"✅ Updated trainer: {updated['name']}")
        
        # Test delete
        deleted = service.delete(created['id'])
        assert deleted is True
        logger.info("✅ Deleted trainer")
        
        # Cleanup test department
        dept_service.delete(test_dept['id'])
        logger.info("✅ Cleaned up test department")
        
        logger.info("🎉 Database Mode: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database Mode failed: {e}")
        return False

def test_legacy_compatibility():
    """Test legacy method compatibility."""
    logger.info("🧪 Testing Legacy Method Compatibility")
    
    # Set environment for JSON mode
    os.environ['USE_SUPABASE'] = 'false'
    
    try:
        from app.services.trainer_service import TrainerService
        from app.models.trainer import TrainerCreate, TrainerUpdate
        
        # Test legacy static methods
        all_trainers = TrainerService.get_all_trainers()
        logger.info(f"✅ Legacy get_all_trainers: {len(all_trainers)} trainers")
        
        dropdown_trainers = TrainerService.get_trainers_for_dropdown()
        logger.info(f"✅ Legacy get_trainers_for_dropdown: {len(dropdown_trainers)} options")
        
        trainers_with_dept = TrainerService.get_trainers_with_department_info()
        logger.info(f"✅ Legacy get_trainers_with_department_info: {len(trainers_with_dept)} trainers")
        
        logger.info("🎉 Legacy Compatibility: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Legacy Compatibility failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("🚀 TrainerService Test Suite")
    logger.info("=" * 50)
    
    # Test JSON mode
    json_success = test_json_mode()
    
    print()  # Add spacing
    
    # Test database mode
    db_success = test_database_mode()
    
    print()  # Add spacing
    
    # Test legacy compatibility
    legacy_success = test_legacy_compatibility()
    
    # Summary
    logger.info("=" * 50)
    logger.info("📊 FINAL RESULTS")
    logger.info(f"JSON File Mode: {'✅ PASSED' if json_success else '❌ FAILED'}")
    logger.info(f"Database Mode: {'✅ PASSED' if db_success else '❌ FAILED'}")
    logger.info(f"Legacy Compatibility: {'✅ PASSED' if legacy_success else '❌ FAILED'}")
    
    overall_success = json_success and db_success and legacy_success
    if overall_success:
        logger.info("🎉 All tests passed! TrainerService is ready.")
    else:
        logger.error("❌ Some tests failed.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
