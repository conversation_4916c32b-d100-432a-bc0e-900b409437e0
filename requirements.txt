﻿# Hospital Equipment Maintenance Management System - Requirements
# Generated from pyproject.toml and actual codebase dependencies

# Core Flask dependencies
Flask==2.3.3
Flask-Login==0.6.3
Flask-Session==0.8.0
Flask-WTF==1.2.1
Werkzeug>=2.3.7
Jinja2>=3.1.2
MarkupSafe>=2.1.1
WTForms>=3.0.0
itsdangerous>=2.1.2
blinker>=1.6.2
click>=8.1.3

# Database dependencies
SQLAlchemy==2.0.23
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5
psycopg2-binary==2.9.9  # PostgreSQL adapter
supabase==2.3.4  # Supabase Python client

# Data validation and processing
pydantic==2.5.0
pandas==2.1.0
python-dotenv==1.0.0

# Scheduling and background tasks
APScheduler==3.10.4
schedule==1.2.0

# Email services and validation
email-validator==2.1.0
mailjet-rest==1.4.0

# Google API integration
google-auth==2.40.3
google-auth-oauthlib==1.2.2
google-api-python-client==2.172.0

# Web push notifications
pywebpush==2.0.3

# Barcode generation
python-barcode==0.15.1
Pillow==10.0.0

# HTTP requests
requests>=2.32.0

# Character encoding detection
chardet>=5.0.0

# Environment variable loading
python-dotenv>=1.0.0

# Production server
gunicorn==23.0.0

# Development dependencies (optional)
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
black>=23.7.0
isort>=5.12.0
flake8>=6.1.0
