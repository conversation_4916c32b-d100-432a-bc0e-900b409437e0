#!/usr/bin/env python3
"""
Create tables in Supabase using direct SQL execution
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_tables():
    """Create tables in Supabase using direct table operations."""
    try:
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.info("Supabase client initialized")
        
        # Test connection by trying to query existing tables
        try:
            result = supabase.table('settings').select('*').limit(1).execute()
            logger.info("Settings table already exists")
        except Exception as e:
            logger.info(f"Settings table doesn't exist or error: {e}")
        
        try:
            result = supabase.table('audit_logs').select('*').limit(1).execute()
            logger.info("Audit logs table already exists")
        except Exception as e:
            logger.info(f"Audit logs table doesn't exist or error: {e}")
        
        try:
            result = supabase.table('push_subscriptions').select('*').limit(1).execute()
            logger.info("Push subscriptions table already exists")
        except Exception as e:
            logger.info(f"Push subscriptions table doesn't exist or error: {e}")
        
        try:
            result = supabase.table('users').select('*').limit(1).execute()
            logger.info("Users table already exists")
        except Exception as e:
            logger.info(f"Users table doesn't exist or error: {e}")
        
        try:
            result = supabase.table('roles').select('*').limit(1).execute()
            logger.info("Roles table already exists")
        except Exception as e:
            logger.info(f"Roles table doesn't exist or error: {e}")
        
        logger.info("Table check completed")
        return True
        
    except Exception as e:
        logger.error(f"Error checking tables: {e}")
        return False

if __name__ == "__main__":
    create_tables()
