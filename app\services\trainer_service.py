"""
Trainer service for managing trainer data.
Supports both JSON files and Supabase database.
"""
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from app.config import Config
from app.models.trainer import Trainer, TrainerCreate, TrainerUpdate
from app.services.base_service import BaseService

logger = logging.getLogger(__name__)

class TrainerService(BaseService):
    """Service for managing trainer data."""

    def __init__(self):
        super().__init__(
            json_file_path='data/trainers.json',
            table_name='trainers'
        )
    
    def get_all(self) -> List[Dict[str, Any]]:
        """Get all trainers."""
        if self.use_database:
            return self._fetch_all("""
                SELECT t.*, d.department_name
                FROM trainers t
                LEFT JOIN departments d ON t.department_id = d.id
                ORDER BY t.name
            """)
        else:
            return self._load_json_data()

    def get_by_id(self, trainer_id: Union[int, str]) -> Optional[Dict[str, Any]]:
        """Get a trainer by ID."""
        if self.use_database:
            return self._fetch_one("""
                SELECT t.*, d.department_name
                FROM trainers t
                LEFT JOIN departments d ON t.department_id = d.id
                WHERE t.id = :id
            """, {'id': int(trainer_id)})
        else:
            data = self._load_json_data()
            for trainer in data:
                if trainer.get('id') == int(trainer_id):
                    return trainer
            return None

    def get_by_name(self, trainer_name: str) -> Optional[Dict[str, Any]]:
        """Get a trainer by name."""
        if self.use_database:
            return self._fetch_one("""
                SELECT t.*, d.department_name
                FROM trainers t
                LEFT JOIN departments d ON t.department_id = d.id
                WHERE t.name = :name
            """, {'name': trainer_name})
        else:
            data = self._load_json_data()
            for trainer in data:
                if trainer.get('name') == trainer_name:
                    return trainer
            return None
    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new trainer."""
        try:
            # Validate input data
            trainer_create = TrainerCreate(**data)

            # Validate department exists if provided
            if trainer_create.department_id:
                from app.services.department_service import DepartmentService
                dept_service = DepartmentService()
                department = dept_service.get_by_id(trainer_create.department_id)
                if not department:
                    logger.error(f"Department with ID {trainer_create.department_id} does not exist")
                    return None

            if self.use_database:
                # Insert into database
                query = """
                    INSERT INTO trainers (name, department_id, telephone, information, created_date, updated_date)
                    VALUES (:name, :department_id, :telephone, :information, NOW(), NOW())
                    RETURNING id, name, department_id, telephone, information, created_date, updated_date
                """

                result = self._execute_query(query, {
                    'name': trainer_create.name,
                    'department_id': trainer_create.department_id,
                    'telephone': trainer_create.telephone,
                    'information': trainer_create.information
                })

                row = result.fetchone()
                if row:
                    columns = result.keys()
                    return dict(zip(columns, row))
                return None

            else:
                # JSON file operations
                json_data = self._load_json_data()

                # Create new trainer
                new_trainer = {
                    'id': self._generate_id(json_data),
                    'name': trainer_create.name,
                    'department_id': trainer_create.department_id,
                    'telephone': trainer_create.telephone,
                    'information': trainer_create.information,
                    'created_date': datetime.now().isoformat(),
                    'updated_date': datetime.now().isoformat()
                }

                json_data.append(new_trainer)

                if self._save_json_data(json_data):
                    return new_trainer
                return None

        except Exception as e:
            logger.error(f"Failed to create trainer: {e}")
            return None
    def update(self, trainer_id: Union[int, str], data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an existing trainer."""
        try:
            # Validate input data
            trainer_update = TrainerUpdate(**data)

            # Validate department exists if provided
            if trainer_update.department_id:
                from app.services.department_service import DepartmentService
                dept_service = DepartmentService()
                department = dept_service.get_by_id(trainer_update.department_id)
                if not department:
                    logger.error(f"Department with ID {trainer_update.department_id} does not exist")
                    return None

            if self.use_database:
                # Check if trainer exists
                existing = self.get_by_id(trainer_id)
                if not existing:
                    logger.error(f"Trainer with ID {trainer_id} not found")
                    return None

                # Build update query
                update_fields = []
                params = {'id': int(trainer_id)}

                if trainer_update.name is not None:
                    update_fields.append("name = :name")
                    params['name'] = trainer_update.name

                if trainer_update.department_id is not None:
                    update_fields.append("department_id = :department_id")
                    params['department_id'] = trainer_update.department_id

                if trainer_update.telephone is not None:
                    update_fields.append("telephone = :telephone")
                    params['telephone'] = trainer_update.telephone

                if trainer_update.information is not None:
                    update_fields.append("information = :information")
                    params['information'] = trainer_update.information

                update_fields.append("updated_date = NOW()")

                query = f"""
                    UPDATE trainers
                    SET {', '.join(update_fields)}
                    WHERE id = :id
                    RETURNING id, name, department_id, telephone, information, created_date, updated_date
                """

                result = self._execute_query(query, params)
                row = result.fetchone()
                if row:
                    columns = result.keys()
                    return dict(zip(columns, row))
                return None

            else:
                # JSON file operations
                json_data = self._load_json_data()

                for i, trainer in enumerate(json_data):
                    if trainer.get('id') == int(trainer_id):
                        # Update fields
                        if trainer_update.name is not None:
                            trainer['name'] = trainer_update.name
                        if trainer_update.department_id is not None:
                            trainer['department_id'] = trainer_update.department_id
                        if trainer_update.telephone is not None:
                            trainer['telephone'] = trainer_update.telephone
                        if trainer_update.information is not None:
                            trainer['information'] = trainer_update.information

                        trainer['updated_date'] = datetime.now().isoformat()

                        if self._save_json_data(json_data):
                            return trainer
                        return None

                logger.error(f"Trainer with ID {trainer_id} not found")
                return None

        except Exception as e:
            logger.error(f"Failed to update trainer: {e}")
            return None
    def delete(self, trainer_id: Union[int, str]) -> bool:
        """Delete a trainer."""
        try:
            if self.use_database:
                # Check if trainer exists
                existing = self.get_by_id(trainer_id)
                if not existing:
                    logger.error(f"Trainer with ID {trainer_id} not found")
                    return False

                # Check if trainer is referenced (would need to check training records, etc.)
                # For now, just delete
                result = self._execute_query(
                    "DELETE FROM trainers WHERE id = :id",
                    {'id': int(trainer_id)}
                )

                return result.rowcount > 0

            else:
                # JSON file operations
                json_data = self._load_json_data()

                for i, trainer in enumerate(json_data):
                    if trainer.get('id') == int(trainer_id):
                        json_data.pop(i)
                        return self._save_json_data(json_data)

                logger.error(f"Trainer with ID {trainer_id} not found")
                return False

        except Exception as e:
            logger.error(f"Failed to delete trainer: {e}")
            return False
    def get_trainers_by_department(self, department_id: int) -> List[Dict[str, Any]]:
        """Get trainers by department ID."""
        if self.use_database:
            return self._fetch_all("""
                SELECT t.*, d.department_name
                FROM trainers t
                LEFT JOIN departments d ON t.department_id = d.id
                WHERE t.department_id = :department_id
                ORDER BY t.name
            """, {'department_id': department_id})
        else:
            data = self._load_json_data()
            return [trainer for trainer in data if trainer.get('department_id') == department_id]

    def get_trainer_options(self) -> List[Dict[str, Any]]:
        """Get trainers formatted for dropdown options."""
        trainers = self.get_all()
        return [
            {
                'id': trainer['id'],
                'name': trainer['name'],
                'department_id': trainer.get('department_id'),
                'value': trainer['name']
            }
            for trainer in sorted(trainers, key=lambda x: x['name'])
        ]

    def search_trainers(self, query: str) -> List[Dict[str, Any]]:
        """Search trainers by name or information."""
        if self.use_database:
            return self._fetch_all("""
                SELECT t.*, d.department_name
                FROM trainers t
                LEFT JOIN departments d ON t.department_id = d.id
                WHERE t.name ILIKE :query OR t.information ILIKE :query
                ORDER BY t.name
            """, {'query': f'%{query}%'})
        else:
            data = self._load_json_data()
            query_lower = query.lower()
            return [
                trainer for trainer in data
                if (query_lower in trainer.get('name', '').lower() or
                    query_lower in trainer.get('information', '').lower())
            ]

    def get_statistics(self) -> Dict[str, Any]:
        """Get trainer statistics."""
        total_trainers = self.count()

        if self.use_database:
            # Get trainers by department
            dept_stats = self._fetch_all("""
                SELECT d.department_name, COUNT(t.id) as trainer_count
                FROM departments d
                LEFT JOIN trainers t ON d.id = t.department_id
                GROUP BY d.id, d.department_name
                HAVING COUNT(t.id) > 0
                ORDER BY trainer_count DESC
            """)
        else:
            # For JSON files, we'd need to load departments.json and count
            dept_stats = []
            trainers = self.get_all()
            dept_counts = {}
            for trainer in trainers:
                dept_id = trainer.get('department_id')
                if dept_id:
                    dept_counts[dept_id] = dept_counts.get(dept_id, 0) + 1

        return {
            'total_trainers': total_trainers,
            'department_stats': dept_stats
        }

    # Legacy methods for backward compatibility
    @staticmethod
    def get_trainers_for_dropdown() -> List[Dict[str, Any]]:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        return service.get_trainer_options()

    @staticmethod
    def get_all_trainers() -> List[Trainer]:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        data = service.get_all()
        return [Trainer.from_dict(trainer_data) for trainer_data in data]

    @staticmethod
    def get_trainer_by_id(trainer_id: int) -> Optional[Trainer]:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        trainer_data = service.get_by_id(trainer_id)
        return Trainer.from_dict(trainer_data) if trainer_data else None

    @staticmethod
    def get_trainers_by_department(department_id: int) -> List[Trainer]:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        data = service.get_trainers_by_department(department_id)
        return [Trainer.from_dict(trainer_data) for trainer_data in data]

    @staticmethod
    def create_trainer(trainer_data: TrainerCreate) -> Trainer:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        trainer_dict = service.create(trainer_data.dict())
        if trainer_dict:
            return Trainer.from_dict(trainer_dict)
        raise ValueError("Failed to create trainer")

    @staticmethod
    def update_trainer(trainer_id: int, update_data: TrainerUpdate) -> Optional[Trainer]:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        trainer_dict = service.update(trainer_id, update_data.dict(exclude_unset=True))
        return Trainer.from_dict(trainer_dict) if trainer_dict else None

    @staticmethod
    def delete_trainer(trainer_id: int) -> bool:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        return service.delete(trainer_id)

    @staticmethod
    def get_trainers_with_department_info() -> List[Dict[str, Any]]:
        """Legacy method for backward compatibility."""
        service = TrainerService()
        return service.get_all()  # Already includes department info in database mode
