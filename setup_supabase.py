#!/usr/bin/env python3
"""
Setup script for Supabase migration.
Installs required dependencies and prepares the environment.
"""
import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a shell command and log the result."""
    logger.info(f"🔄 {description}...")
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed:")
        logger.error(f"Command: {command}")
        logger.error(f"Error: {e.stderr}")
        return False

def install_dependencies():
    """Install required Python dependencies."""
    logger.info("📦 Installing Python dependencies...")
    
    # Core dependencies for Supabase
    dependencies = [
        "SQLAlchemy==2.0.23",
        "Flask-SQLAlchemy==3.1.1", 
        "Flask-Migrate==4.0.5",
        "psycopg2-binary==2.9.9",
        "supabase==2.3.4"
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    
    return True

def verify_installation():
    """Verify that all dependencies are installed correctly."""
    logger.info("🔍 Verifying installation...")
    
    try:
        import sqlalchemy
        import flask_sqlalchemy
        import flask_migrate
        import psycopg2
        import supabase
        
        logger.info(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
        logger.info(f"✅ Flask-SQLAlchemy: {flask_sqlalchemy.__version__}")
        logger.info(f"✅ Flask-Migrate: {flask_migrate.__version__}")
        logger.info(f"✅ psycopg2: {psycopg2.__version__}")
        logger.info(f"✅ supabase: {supabase.__version__}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False

def create_backup():
    """Create backup of current data files."""
    logger.info("💾 Creating backup of current data files...")
    
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"data/backups/pre_supabase_migration_{timestamp}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        data_files = [
            'ppm.json',
            'ocm.json', 
            'training.json',
            'departments.json',
            'trainers.json',
            'settings.json',
            'audit_log.json',
            'equipment_history.json'
        ]
        
        for file in data_files:
            src = f"data/{file}"
            if os.path.exists(src):
                dst = f"{backup_dir}/{file}"
                shutil.copy2(src, dst)
                logger.info(f"✅ Backed up {file}")
        
        logger.info(f"✅ Backup created in: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        logger.error(f"❌ Backup creation failed: {e}")
        return None

def display_next_steps():
    """Display next steps for the user."""
    logger.info("\n" + "=" * 60)
    logger.info("🎯 NEXT STEPS")
    logger.info("=" * 60)
    logger.info("1. Set up your Supabase project:")
    logger.info("   - Go to https://supabase.com")
    logger.info("   - Create a new project: 'alorf-biomed-system'")
    logger.info("   - Note down your project credentials")
    logger.info("")
    logger.info("2. Update your .env file with Supabase credentials:")
    logger.info("   - SUPABASE_URL=https://your-project-ref.supabase.co")
    logger.info("   - SUPABASE_ANON_KEY=your-anon-key")
    logger.info("   - SUPABASE_SERVICE_KEY=your-service-key")
    logger.info("   - DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres")
    logger.info("")
    logger.info("3. Test your connection:")
    logger.info("   python test_supabase_connection.py")
    logger.info("")
    logger.info("4. Create database schema:")
    logger.info("   python create_supabase_schema.py")
    logger.info("")
    logger.info("5. Migrate your data:")
    logger.info("   python migrate_to_supabase.py")
    logger.info("=" * 60)

def main():
    """Main setup function."""
    logger.info("🚀 ALORF BIOMED Supabase Migration Setup")
    logger.info("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8 or higher is required")
        return False
    
    logger.info(f"✅ Python version: {sys.version}")
    
    # Create backup
    backup_dir = create_backup()
    if not backup_dir:
        logger.error("❌ Failed to create backup. Aborting setup.")
        return False
    
    # Install dependencies
    if not install_dependencies():
        logger.error("❌ Failed to install dependencies")
        return False
    
    # Verify installation
    if not verify_installation():
        logger.error("❌ Installation verification failed")
        return False
    
    logger.info("✅ Setup completed successfully!")
    display_next_steps()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
