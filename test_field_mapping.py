#!/usr/bin/env python3
"""
Test field mapping fix
"""

import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.supabase_equipment_service import get_equipment_service

def main():
    print("Testing field mapping...")
    
    es = get_equipment_service()
    
    # Test PPM
    ppm = es.load_equipment_data('ppm')
    if ppm:
        sample = ppm[0]
        print(f"PPM Sample:")
        print(f"  SERIAL: {sample.get('SERIAL')}")
        print(f"  Name: {sample.get('Name')}")
        print(f"  Department: {sample.get('Department')}")
        print(f"  MODEL: {sample.get('MODEL')}")
        print(f"  MANUFACTURER: {sample.get('MANUFACTURER')}")
        print(f"  PPM_Q_I: {sample.get('PPM_Q_I')}")
        print(f"  Raw serial: {sample.get('serial')}")
        print(f"  Raw name: {sample.get('name')}")
        print(f"  Keys: {list(sample.keys())[:20]}")

    # Test OCM
    ocm = es.load_equipment_data('ocm')
    if ocm:
        sample = ocm[0]
        print(f"OCM Sample:")
        print(f"  Serial: {sample.get('Serial')}")
        print(f"  Name: {sample.get('Name')}")
        print(f"  Department: {sample.get('Department')}")
        print(f"  MODEL: {sample.get('MODEL')}")
        print(f"  Raw serial: {sample.get('serial')}")
        print(f"  Raw name: {sample.get('name')}")

if __name__ == "__main__":
    main()
