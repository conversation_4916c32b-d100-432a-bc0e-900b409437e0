#!/usr/bin/env python3
"""
Check equipment tables in Supabase database
"""

import os
import sys
import logging

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_equipment_tables():
    """Check if equipment tables exist in Supabase and their contents."""
    try:
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.info("Supabase client initialized")
        
        # Check for PPM table
        logger.info("=== Checking PPM Equipment Table ===")
        try:
            ppm_result = supabase.table('ppm_equipment').select('*').limit(5).execute()
            logger.info(f"✅ PPM table exists with {len(ppm_result.data)} records (showing first 5)")
            for i, record in enumerate(ppm_result.data):
                logger.info(f"  PPM {i+1}: {record.get('Name', 'Unknown')} - {record.get('Department', 'Unknown')}")
        except Exception as e:
            logger.warning(f"❌ PPM table issue: {e}")
        
        # Check for OCM table
        logger.info("=== Checking OCM Equipment Table ===")
        try:
            ocm_result = supabase.table('ocm_equipment').select('*').limit(5).execute()
            logger.info(f"✅ OCM table exists with {len(ocm_result.data)} records (showing first 5)")
            for i, record in enumerate(ocm_result.data):
                logger.info(f"  OCM {i+1}: {record.get('Name', 'Unknown')} - {record.get('Department', 'Unknown')}")
        except Exception as e:
            logger.warning(f"❌ OCM table issue: {e}")
        
        # Check what tables exist
        logger.info("=== Checking Available Tables ===")
        try:
            # Try to get table information
            tables_to_check = ['ppm_equipment', 'ocm_equipment', 'equipment_ppm', 'equipment_ocm', 'ppm', 'ocm']
            
            for table_name in tables_to_check:
                try:
                    result = supabase.table(table_name).select('*').limit(1).execute()
                    logger.info(f"✅ Table '{table_name}' exists")
                except Exception as e:
                    logger.info(f"❌ Table '{table_name}' does not exist or has issues: {e}")
        
        except Exception as e:
            logger.error(f"Error checking tables: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking equipment tables: {e}")
        return False

def main():
    """Main function."""
    logger.info("=== ALORF BIOMED Equipment Tables Check Started ===")
    
    success = check_equipment_tables()
    
    if success:
        logger.info("✅ Equipment tables check completed")
    else:
        logger.error("❌ Equipment tables check failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
