#!/usr/bin/env python3
"""
Comprehensive verification of ALORF BIOMED database configuration and data source.
Tests current functionality and determines active database mode.
"""
import os
import sys
import logging
import shutil
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_env_configuration():
    """Check current environment configuration."""
    logger.info("🔍 Checking Environment Configuration...")
    
    use_supabase = os.getenv('USE_SUPABASE', 'false').lower()
    database_url = os.getenv('DATABASE_URL', 'Not set')
    supabase_url = os.getenv('SUPABASE_URL', 'Not set')
    
    logger.info(f"✅ USE_SUPABASE: {use_supabase}")
    logger.info(f"✅ DATABASE_URL: {database_url[:50]}...")
    logger.info(f"✅ SUPABASE_URL: {supabase_url}")
    
    return use_supabase == 'true'

def test_current_data_source():
    """Test which data source is currently being used."""
    logger.info("🧪 Testing Current Data Source...")
    
    try:
        from app.services.department_service import DepartmentService
        from app.services.trainer_service import TrainerService
        
        # Test DepartmentService
        dept_service = DepartmentService()
        departments = dept_service.get_all()
        logger.info(f"✅ DepartmentService loaded {len(departments)} departments")
        
        # Check if it's using database mode
        is_db_mode = hasattr(dept_service, 'use_database') and dept_service.use_database
        logger.info(f"✅ DepartmentService database mode: {is_db_mode}")
        
        # Test TrainerService
        trainer_service = TrainerService()
        trainers = trainer_service.get_all()
        logger.info(f"✅ TrainerService loaded {len(trainers)} trainers")
        
        # Check trainer database mode
        trainer_db_mode = hasattr(trainer_service, 'use_database') and trainer_service.use_database
        logger.info(f"✅ TrainerService database mode: {trainer_db_mode}")
        
        return {
            'departments_count': len(departments),
            'trainers_count': len(trainers),
            'dept_db_mode': is_db_mode,
            'trainer_db_mode': trainer_db_mode
        }
        
    except Exception as e:
        logger.error(f"❌ Data source test failed: {e}")
        return None

def test_database_operations():
    """Test CRUD operations to verify active database."""
    logger.info("⚙️ Testing Database Operations...")
    
    try:
        from app.services.department_service import DepartmentService
        
        dept_service = DepartmentService()
        
        # Test CREATE
        test_dept_name = f"Test Dept {datetime.now().strftime('%H%M%S')}"
        created_dept = dept_service.create_department(test_dept_name)
        logger.info(f"✅ CREATE: Created department '{test_dept_name}'")
        
        # Test READ
        all_depts = dept_service.get_all()
        found_dept = next((d for d in all_depts if d['name'] == test_dept_name), None)
        if found_dept:
            logger.info(f"✅ READ: Found created department")
        else:
            logger.error("❌ READ: Could not find created department")
            return False
        
        # Test UPDATE
        updated_name = f"Updated {test_dept_name}"
        dept_service.update_department(found_dept['id'], updated_name)
        updated_depts = dept_service.get_all()
        updated_dept = next((d for d in updated_depts if d['id'] == found_dept['id']), None)
        if updated_dept and updated_dept['name'] == updated_name:
            logger.info(f"✅ UPDATE: Successfully updated department name")
        else:
            logger.error("❌ UPDATE: Failed to update department")
            return False
        
        # Test DELETE
        dept_service.delete_department(found_dept['id'])
        final_depts = dept_service.get_all()
        deleted_dept = next((d for d in final_depts if d['id'] == found_dept['id']), None)
        if not deleted_dept:
            logger.info(f"✅ DELETE: Successfully deleted department")
        else:
            logger.error("❌ DELETE: Failed to delete department")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_supabase_data_counts():
    """Verify data counts in Supabase match migration expectations."""
    logger.info("📊 Verifying Supabase Data Counts...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        expected_counts = {
            'departments': 28,
            'trainers': 12,
            'ppm_equipment': 1040,
            'ocm_equipment': 610,
            'training_records': 239
        }
        
        actual_counts = {}
        
        with engine.connect() as conn:
            for table, expected in expected_counts.items():
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                actual = result.fetchone()[0]
                actual_counts[table] = actual
                
                if actual >= expected:
                    logger.info(f"✅ {table}: {actual} records (expected ≥{expected})")
                else:
                    logger.error(f"❌ {table}: {actual} records (expected ≥{expected})")
        
        return actual_counts
        
    except Exception as e:
        logger.error(f"❌ Supabase data verification failed: {e}")
        return None

def test_without_json_files():
    """Test application functionality without JSON files."""
    logger.info("🗂️ Testing Application Without JSON Files...")
    
    data_dir = "data"
    backup_dir = "data_backup_temp"
    
    try:
        # Check if data directory exists
        if not os.path.exists(data_dir):
            logger.info("✅ Data directory doesn't exist - application should work with Supabase only")
            return test_services_without_json()
        
        # Backup data directory
        logger.info(f"📁 Backing up {data_dir} to {backup_dir}")
        if os.path.exists(backup_dir):
            shutil.rmtree(backup_dir)
        shutil.copytree(data_dir, backup_dir)
        
        # Remove data directory
        logger.info(f"🗑️ Temporarily removing {data_dir}")
        shutil.rmtree(data_dir)
        
        # Test services without JSON files
        result = test_services_without_json()
        
        return result
        
    except Exception as e:
        logger.error(f"❌ JSON file test failed: {e}")
        return False
    finally:
        # Restore data directory
        if os.path.exists(backup_dir):
            logger.info(f"🔄 Restoring {data_dir} from backup")
            if os.path.exists(data_dir):
                shutil.rmtree(data_dir)
            shutil.move(backup_dir, data_dir)

def test_services_without_json():
    """Test services functionality without JSON files."""
    try:
        # Clear any cached modules to ensure fresh imports
        modules_to_clear = [
            'app.services.department_service',
            'app.services.trainer_service'
        ]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        from app.services.department_service import DepartmentService
        from app.services.trainer_service import TrainerService
        
        # Test DepartmentService
        dept_service = DepartmentService()
        departments = dept_service.get_all()
        logger.info(f"✅ DepartmentService (no JSON): {len(departments)} departments")
        
        # Test TrainerService
        trainer_service = TrainerService()
        trainers = trainer_service.get_all()
        logger.info(f"✅ TrainerService (no JSON): {len(trainers)} trainers")
        
        # Test operations
        if len(departments) >= 28 and len(trainers) >= 12:
            logger.info("✅ Services work correctly without JSON files")
            return True
        else:
            logger.error(f"❌ Unexpected counts without JSON - Departments: {len(departments)}, Trainers: {len(trainers)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Services test without JSON failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_json_dependencies():
    """Check which parts of the application still depend on JSON files."""
    logger.info("🔗 Checking JSON File Dependencies...")
    
    json_dependent_services = []
    
    try:
        # Check if settings still use JSON
        settings_file = "data/settings.json"
        if os.path.exists(settings_file):
            logger.info("⚠️ Settings still stored in JSON file")
            json_dependent_services.append("Settings")
        
        # Check if audit log uses JSON
        audit_file = "data/audit_log.json"
        if os.path.exists(audit_file):
            logger.info("⚠️ Audit log still stored in JSON file")
            json_dependent_services.append("Audit Log")
        
        # Check if push subscriptions use JSON
        push_file = "data/push_subscriptions.json"
        if os.path.exists(push_file):
            logger.info("⚠️ Push subscriptions still stored in JSON file")
            json_dependent_services.append("Push Subscriptions")
        
        if not json_dependent_services:
            logger.info("✅ No critical JSON dependencies found")
        else:
            logger.info(f"⚠️ JSON dependencies found: {', '.join(json_dependent_services)}")
        
        return json_dependent_services
        
    except Exception as e:
        logger.error(f"❌ JSON dependency check failed: {e}")
        return ["Unknown - Check failed"]

def main():
    """Main verification function."""
    logger.info("🚀 ALORF BIOMED Database Configuration Verification")
    logger.info("=" * 70)
    
    # 1. Check environment configuration
    is_supabase_enabled = check_env_configuration()
    
    # 2. Test current data source
    logger.info("\n" + "=" * 70)
    data_source_info = test_current_data_source()
    
    # 3. Test database operations
    logger.info("\n" + "=" * 70)
    crud_success = test_database_operations()
    
    # 4. Verify Supabase data counts
    logger.info("\n" + "=" * 70)
    supabase_counts = verify_supabase_data_counts()
    
    # 5. Test without JSON files
    logger.info("\n" + "=" * 70)
    no_json_success = test_without_json_files()
    
    # 6. Check JSON dependencies
    logger.info("\n" + "=" * 70)
    json_deps = check_json_dependencies()
    
    # Summary
    logger.info("\n" + "=" * 70)
    logger.info("📊 DATABASE CONFIGURATION VERIFICATION SUMMARY")
    logger.info("=" * 70)
    
    logger.info(f"Environment Configuration:")
    logger.info(f"  USE_SUPABASE: {'✅ TRUE' if is_supabase_enabled else '❌ FALSE'}")
    
    if data_source_info:
        logger.info(f"Current Data Source:")
        logger.info(f"  Departments: {data_source_info['departments_count']} records")
        logger.info(f"  Trainers: {data_source_info['trainers_count']} records")
        logger.info(f"  Database Mode: {'✅ ACTIVE' if data_source_info.get('dept_db_mode') else '❌ INACTIVE'}")
    
    logger.info(f"Database Operations: {'✅ WORKING' if crud_success else '❌ FAILED'}")
    logger.info(f"Supabase Data: {'✅ VERIFIED' if supabase_counts else '❌ FAILED'}")
    logger.info(f"Works Without JSON: {'✅ YES' if no_json_success else '❌ NO'}")
    logger.info(f"JSON Dependencies: {len(json_deps)} services")
    
    # Final assessment
    logger.info("\n" + "=" * 70)
    logger.info("🎯 FINAL ASSESSMENT")
    logger.info("=" * 70)
    
    if (is_supabase_enabled and crud_success and supabase_counts and no_json_success):
        logger.info("🎉 MIGRATION COMPLETE AND SUCCESSFUL!")
        logger.info("✅ Application is fully operational with Supabase PostgreSQL")
        logger.info("✅ JSON files can be safely removed for core functionality")
        logger.info("✅ All database operations working correctly")
        
        if json_deps:
            logger.info(f"⚠️ Note: {len(json_deps)} services still use JSON files:")
            for dep in json_deps:
                logger.info(f"   - {dep}")
            logger.info("   These can be migrated separately if needed")
        
        return True
    else:
        logger.error("❌ MIGRATION INCOMPLETE OR ISSUES DETECTED")
        logger.error("   Please review the test results above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
