#!/usr/bin/env python3
"""
Create database schema in Supabase for ALORF BIOMED System.
This script creates all required tables with identical structure to current data models.
"""
import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# SQL statements for creating tables
CREATE_TABLES_SQL = {
    'departments': """
    CREATE TABLE IF NOT EXISTS departments (
        id SERIAL PRIMARY KEY,
        department_name VARCHAR(100) NOT NULL UNIQUE,
        information TEXT,
        created_date TIMESTAMP DEFAULT NOW(),
        updated_date TIMESTAMP DEFAULT NOW()
    );
    
    -- Create index for faster lookups
    CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(department_name);
    """,
    
    'trainers': """
    CREATE TABLE IF NOT EXISTS trainers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        department_id INTEGER REFERENCES departments(id) ON DELETE SET NULL,
        telephone VARCHAR(20),
        information TEXT,
        created_date TIMESTAMP DEFAULT NOW(),
        updated_date TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_trainers_name ON trainers(name);
    CREATE INDEX IF NOT EXISTS idx_trainers_department ON trainers(department_id);
    """,
    
    'ppm_equipment': """
    CREATE TABLE IF NOT EXISTS ppm_equipment (
        id SERIAL PRIMARY KEY,
        no INTEGER,
        department VARCHAR(50) NOT NULL,
        name VARCHAR(200),
        model VARCHAR(100) NOT NULL,
        serial VARCHAR(100) NOT NULL UNIQUE,
        manufacturer VARCHAR(100) NOT NULL,
        log_number VARCHAR(50) NOT NULL,
        installation_date VARCHAR(20),
        warranty_end VARCHAR(20),
        status VARCHAR(20) DEFAULT 'Upcoming',
        has_history BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_ppm_equipment_serial ON ppm_equipment(serial);
    CREATE INDEX IF NOT EXISTS idx_ppm_equipment_department ON ppm_equipment(department);
    CREATE INDEX IF NOT EXISTS idx_ppm_equipment_status ON ppm_equipment(status);
    CREATE INDEX IF NOT EXISTS idx_ppm_equipment_log_number ON ppm_equipment(log_number);
    """,
    
    'ppm_quarters': """
    CREATE TABLE IF NOT EXISTS ppm_quarters (
        id SERIAL PRIMARY KEY,
        ppm_equipment_id INTEGER REFERENCES ppm_equipment(id) ON DELETE CASCADE,
        quarter VARCHAR(10) NOT NULL, -- 'Q_I', 'Q_II', 'Q_III', 'Q_IV'
        engineer VARCHAR(100),
        quarter_date VARCHAR(20),
        status VARCHAR(20),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_ppm_quarters_equipment ON ppm_quarters(ppm_equipment_id);
    CREATE INDEX IF NOT EXISTS idx_ppm_quarters_quarter ON ppm_quarters(quarter);
    CREATE INDEX IF NOT EXISTS idx_ppm_quarters_engineer ON ppm_quarters(engineer);
    """,
    
    'ocm_equipment': """
    CREATE TABLE IF NOT EXISTS ocm_equipment (
        id SERIAL PRIMARY KEY,
        no INTEGER,
        department VARCHAR(50) NOT NULL,
        name VARCHAR(200) NOT NULL,
        model VARCHAR(100) NOT NULL,
        serial VARCHAR(100) NOT NULL UNIQUE,
        manufacturer VARCHAR(100) NOT NULL,
        log_number VARCHAR(50) NOT NULL,
        installation_date VARCHAR(20),
        warranty_end VARCHAR(20),
        service_date VARCHAR(20),
        engineer VARCHAR(100) NOT NULL,
        next_maintenance VARCHAR(20),
        status VARCHAR(20) DEFAULT 'Upcoming',
        has_history BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_ocm_equipment_serial ON ocm_equipment(serial);
    CREATE INDEX IF NOT EXISTS idx_ocm_equipment_department ON ocm_equipment(department);
    CREATE INDEX IF NOT EXISTS idx_ocm_equipment_status ON ocm_equipment(status);
    CREATE INDEX IF NOT EXISTS idx_ocm_equipment_engineer ON ocm_equipment(engineer);
    CREATE INDEX IF NOT EXISTS idx_ocm_equipment_log_number ON ocm_equipment(log_number);
    """,
    
    'training_records': """
    CREATE TABLE IF NOT EXISTS training_records (
        id SERIAL PRIMARY KEY,
        employee_id VARCHAR(20),
        name VARCHAR(200),
        department VARCHAR(50),
        last_trained_date VARCHAR(20),
        next_due_date VARCHAR(20),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_training_records_employee ON training_records(employee_id);
    CREATE INDEX IF NOT EXISTS idx_training_records_department ON training_records(department);
    CREATE INDEX IF NOT EXISTS idx_training_records_name ON training_records(name);
    """,
    
    'machine_trainer_assignments': """
    CREATE TABLE IF NOT EXISTS machine_trainer_assignments (
        id SERIAL PRIMARY KEY,
        training_record_id INTEGER REFERENCES training_records(id) ON DELETE CASCADE,
        machine VARCHAR(200),
        trainer VARCHAR(100),
        created_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_mta_training_record ON machine_trainer_assignments(training_record_id);
    CREATE INDEX IF NOT EXISTS idx_mta_machine ON machine_trainer_assignments(machine);
    CREATE INDEX IF NOT EXISTS idx_mta_trainer ON machine_trainer_assignments(trainer);
    """,
    
    'application_settings': """
    CREATE TABLE IF NOT EXISTS application_settings (
        id SERIAL PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create index
    CREATE INDEX IF NOT EXISTS idx_app_settings_key ON application_settings(setting_key);
    """,
    
    'audit_logs': """
    CREATE TABLE IF NOT EXISTS audit_logs (
        id SERIAL PRIMARY KEY,
        timestamp TIMESTAMP DEFAULT NOW(),
        user_id VARCHAR(50),
        action VARCHAR(100),
        table_name VARCHAR(50),
        record_id VARCHAR(50),
        old_values JSONB,
        new_values JSONB,
        ip_address VARCHAR(45)
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
    CREATE INDEX IF NOT EXISTS idx_audit_logs_user ON audit_logs(user_id);
    CREATE INDEX IF NOT EXISTS idx_audit_logs_table ON audit_logs(table_name);
    """,
    
    'equipment_history': """
    CREATE TABLE IF NOT EXISTS equipment_history (
        id SERIAL PRIMARY KEY,
        equipment_type VARCHAR(10), -- 'PPM' or 'OCM'
        equipment_id INTEGER,
        serial_number VARCHAR(100),
        note TEXT,
        attachment_filename VARCHAR(255),
        attachment_data BYTEA,
        created_at TIMESTAMP DEFAULT NOW(),
        created_by VARCHAR(100)
    );

    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_equipment_history_type ON equipment_history(equipment_type);
    CREATE INDEX IF NOT EXISTS idx_equipment_history_equipment ON equipment_history(equipment_id);
    CREATE INDEX IF NOT EXISTS idx_equipment_history_serial ON equipment_history(serial_number);
    CREATE INDEX IF NOT EXISTS idx_equipment_history_created ON equipment_history(created_at);
    """,

    'push_subscriptions': """
    CREATE TABLE IF NOT EXISTS push_subscriptions (
        id SERIAL PRIMARY KEY,
        endpoint TEXT NOT NULL,
        p256dh_key TEXT NOT NULL,
        auth_key TEXT NOT NULL,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );

    -- Create index
    CREATE INDEX IF NOT EXISTS idx_push_subscriptions_endpoint ON push_subscriptions(endpoint);
    """
}

def test_connection():
    """Test database connection before creating schema."""
    logger.info("🔍 Testing database connection...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            logger.error("❌ DATABASE_URL not set in environment")
            return False
        
        engine = create_engine(database_url, pool_pre_ping=True)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ Connected to PostgreSQL: {version[:50]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

def create_table(table_name, sql_statement):
    """Create a single table."""
    logger.info(f"📋 Creating table: {table_name}")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        with engine.connect() as conn:
            # Execute the SQL statement
            conn.execute(text(sql_statement))
            conn.commit()
            logger.info(f"✅ Table '{table_name}' created successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create table '{table_name}': {e}")
        return False

def verify_tables():
    """Verify that all tables were created successfully."""
    logger.info("🔍 Verifying table creation...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        expected_tables = list(CREATE_TABLES_SQL.keys())
        
        with engine.connect() as conn:
            # Get list of tables
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            """))
            
            existing_tables = [row[0] for row in result.fetchall()]
            
            logger.info(f"📊 Found {len(existing_tables)} tables in database:")
            for table in existing_tables:
                logger.info(f"  ✅ {table}")
            
            # Check if all expected tables exist
            missing_tables = set(expected_tables) - set(existing_tables)
            if missing_tables:
                logger.error(f"❌ Missing tables: {', '.join(missing_tables)}")
                return False
            
            logger.info("✅ All tables created successfully!")
            return True
        
    except Exception as e:
        logger.error(f"❌ Table verification failed: {e}")
        return False

def main():
    """Main function to create database schema."""
    logger.info("🚀 Creating Supabase Database Schema for ALORF BIOMED")
    logger.info("=" * 60)
    
    # Test connection first
    if not test_connection():
        logger.error("❌ Cannot proceed without database connection")
        return False
    
    # Create tables in order (respecting foreign key dependencies)
    table_order = [
        'departments',
        'trainers',
        'ppm_equipment',
        'ppm_quarters',
        'ocm_equipment',
        'training_records',
        'machine_trainer_assignments',
        'application_settings',
        'audit_logs',
        'equipment_history',
        'push_subscriptions'
    ]
    
    success_count = 0
    total_tables = len(table_order)
    
    for table_name in table_order:
        if table_name in CREATE_TABLES_SQL:
            if create_table(table_name, CREATE_TABLES_SQL[table_name]):
                success_count += 1
            else:
                logger.error(f"❌ Failed to create {table_name}. Stopping.")
                break
    
    logger.info("=" * 60)
    logger.info(f"📊 Schema Creation Summary: {success_count}/{total_tables} tables created")
    
    if success_count == total_tables:
        # Verify all tables
        if verify_tables():
            logger.info("🎉 Database schema created successfully!")
            logger.info("✅ Ready for data migration")
            return True
    
    logger.error("❌ Schema creation incomplete. Please check errors above.")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
