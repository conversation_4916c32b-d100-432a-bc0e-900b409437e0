#!/usr/bin/env python3
"""
Comprehensive test to verify complete Supabase migration.
Tests all migrated data and application functionality.
"""
import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test Supabase database connection."""
    logger.info("🔍 Testing Supabase database connection...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ Connected to PostgreSQL: {version[:50]}...")
        
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

def test_migrated_data():
    """Test all migrated data counts."""
    logger.info("📊 Verifying migrated data...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        expected_counts = {
            'departments': 28,
            'trainers': 12,
            'ppm_equipment': 1040,
            'ppm_quarters': 4160,
            'ocm_equipment': 610,
            'training_records': 239
        }
        
        with engine.connect() as conn:
            for table, expected in expected_counts.items():
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                actual = result.fetchone()[0]
                
                if actual >= expected:  # Allow for test data additions
                    logger.info(f"✅ {table}: {actual} records (expected ≥{expected})")
                else:
                    logger.error(f"❌ {table}: {actual} records (expected ≥{expected})")
                    return False
        
        return True
    except Exception as e:
        logger.error(f"❌ Data verification failed: {e}")
        return False

def test_services_with_database():
    """Test services with database mode."""
    logger.info("🧪 Testing services with Supabase database...")
    
    # Set database mode
    os.environ['USE_SUPABASE'] = 'true'
    
    try:
        from app.services.department_service import DepartmentService
        from app.services.trainer_service import TrainerService
        
        # Test DepartmentService
        dept_service = DepartmentService()
        departments = dept_service.get_all()
        logger.info(f"✅ DepartmentService: {len(departments)} departments loaded")
        
        # Test TrainerService
        trainer_service = TrainerService()
        trainers = trainer_service.get_all()
        logger.info(f"✅ TrainerService: {len(trainers)} trainers loaded")
        
        # Test department-trainer relationship
        if len(trainers) > 0:
            trainer_with_dept = [t for t in trainers if t.get('department_name')]
            logger.info(f"✅ Relationships: {len(trainer_with_dept)} trainers have department info")
        
        return True
    except Exception as e:
        logger.error(f"❌ Service testing failed: {e}")
        return False

def test_data_integrity():
    """Test data integrity and relationships."""
    logger.info("🔗 Testing data integrity and relationships...")
    
    try:
        from sqlalchemy import create_engine, text
        
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url, pool_pre_ping=True)
        
        with engine.connect() as conn:
            # Test PPM equipment and quarters relationship
            ppm_result = conn.execute(text("""
                SELECT COUNT(DISTINCT pe.id) as equipment_count, COUNT(pq.id) as quarter_count
                FROM ppm_equipment pe
                LEFT JOIN ppm_quarters pq ON pe.id = pq.ppm_equipment_id
            """))
            ppm_data = ppm_result.fetchone()
            logger.info(f"✅ PPM Integrity: {ppm_data[0]} equipment with {ppm_data[1]} quarters")
            
            # Test trainer-department relationship
            trainer_result = conn.execute(text("""
                SELECT COUNT(t.id) as trainers_with_dept
                FROM trainers t
                INNER JOIN departments d ON t.department_id = d.id
            """))
            trainer_count = trainer_result.fetchone()[0]
            logger.info(f"✅ Trainer-Department Integrity: {trainer_count} trainers linked to departments")
            
            # Test unique constraints
            serial_check = conn.execute(text("""
                SELECT COUNT(*) as total, COUNT(DISTINCT serial) as unique_serials
                FROM ppm_equipment
            """))
            serial_data = serial_check.fetchone()
            if serial_data[0] == serial_data[1]:
                logger.info(f"✅ PPM Serial Uniqueness: {serial_data[1]} unique serials")
            else:
                logger.error(f"❌ PPM Serial Duplicates: {serial_data[0]} total vs {serial_data[1]} unique")
                return False
        
        return True
    except Exception as e:
        logger.error(f"❌ Data integrity test failed: {e}")
        return False

def test_application_functionality():
    """Test key application functionality."""
    logger.info("⚙️ Testing application functionality...")
    
    # Set database mode
    os.environ['USE_SUPABASE'] = 'true'
    
    try:
        from app.services.department_service import DepartmentService
        from app.services.trainer_service import TrainerService
        
        # Test search functionality
        dept_service = DepartmentService()
        search_results = dept_service.search_departments("ICU")
        logger.info(f"✅ Department Search: Found {len(search_results)} results for 'ICU'")
        
        # Test dropdown options
        dept_options = dept_service.get_department_options()
        logger.info(f"✅ Department Options: {len(dept_options)} options for dropdowns")
        
        # Test trainer functionality
        trainer_service = TrainerService()
        trainer_options = trainer_service.get_trainer_options()
        logger.info(f"✅ Trainer Options: {len(trainer_options)} options for dropdowns")
        
        # Test statistics
        dept_stats = dept_service.get_statistics()
        logger.info(f"✅ Department Statistics: {dept_stats['total_departments']} total departments")
        
        return True
    except Exception as e:
        logger.error(f"❌ Application functionality test failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("🚀 ALORF BIOMED Migration Verification Suite")
    logger.info("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Migrated Data Verification", test_migrated_data),
        ("Services with Database", test_services_with_database),
        ("Data Integrity", test_data_integrity),
        ("Application Functionality", test_application_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} test...")
        try:
            if test_func():
                logger.info(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 MIGRATION VERIFICATION SUMMARY")
    logger.info("=" * 60)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASSED" if i < passed else "❌ FAILED"
        logger.info(f"{test_name:.<35} {status}")
    
    logger.info("=" * 60)
    logger.info(f"📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 MIGRATION VERIFICATION SUCCESSFUL!")
        logger.info("✅ Your ALORF BIOMED system is ready to use Supabase!")
        logger.info("✅ All data migrated successfully with full functionality!")
        return True
    else:
        logger.error("❌ Some verification tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
