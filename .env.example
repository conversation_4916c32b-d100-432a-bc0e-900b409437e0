# Flask Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True

# Email Configuration - Replace with your actual Mailjet credentials
MAILJET_API_KEY=your_mailjet_api_key
MAILJET_SECRET_KEY=your_mailjet_secret_key
EMAIL_SENDER=<EMAIL>
EMAIL_RECEIVER=<EMAIL>

# Reminder Configuration
REMINDER_DAYS=60
SCHEDULER_ENABLED=True
# SCHEDULER_INTERVAL is managed via settings.json (email_reminder_interval_minutes)

# VAPID Keys for Web Push Notifications (Generate these for your application)
VAPID_PRIVATE_KEY=your_generated_vapid_private_key
VAPID_PUBLIC_KEY=your_generated_vapid_public_key
VAPID_SUBJECT=mailto:<EMAIL>
