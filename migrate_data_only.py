#!/usr/bin/env python3
"""
Migrate data from JSON files to existing Supabase tables
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataMigration:
    def __init__(self):
        """Initialize the migration with Supabase client."""
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.info("Supabase client initialized")

    def migrate_audit_logs(self) -> bool:
        """Migrate audit logs from JSON to Supabase."""
        try:
            logger.info("Starting audit logs migration...")
            
            # Load audit logs - try both possible paths
            audit_paths = [
                Path('data/audit_log.json'),
                Path('app/data/audit_log.json'),
                Path(Config.AUDIT_LOG_JSON_PATH)
            ]
            
            audit_logs = None
            audit_path = None
            
            for path in audit_paths:
                if path.exists():
                    audit_path = path
                    with open(path, 'r') as f:
                        audit_logs = json.load(f)
                    break
            
            if audit_logs is None:
                logger.warning("No audit log file found")
                return True
            
            logger.info(f"Loaded {len(audit_logs)} audit log entries from {audit_path}")
            
            # Check if data already exists
            existing_result = self.supabase.table('audit_logs').select('*').limit(1).execute()
            if existing_result.data:
                logger.info("Audit logs table already contains data, skipping migration")
                return True
            
            # Migrate in batches
            batch_size = 50
            total_batches = (len(audit_logs) + batch_size - 1) // batch_size
            
            for i in range(0, len(audit_logs), batch_size):
                batch = audit_logs[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                logger.info(f"Migrating batch {batch_num}/{total_batches} ({len(batch)} entries)")
                
                # Prepare batch data
                batch_records = []
                for log_entry in batch:
                    # Convert timestamp to proper format
                    timestamp_str = log_entry.get('timestamp', '')
                    try:
                        # Parse the timestamp and convert to ISO format
                        dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        iso_timestamp = dt.isoformat()
                    except:
                        iso_timestamp = datetime.now().isoformat()
                    
                    record = {
                        'timestamp': iso_timestamp,
                        'event_type': log_entry.get('event_type', 'Unknown'),
                        'performed_by': log_entry.get('performed_by', 'System'),
                        'description': log_entry.get('description', ''),
                        'status': log_entry.get('status', 'Success'),
                        'details': log_entry.get('details', {})
                    }
                    batch_records.append(record)
                
                # Insert batch
                result = self.supabase.table('audit_logs').insert(batch_records).execute()
                logger.info(f"Batch {batch_num} migrated successfully")
            
            logger.info("Audit logs migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate audit logs: {e}")
            return False

    def migrate_push_subscriptions(self) -> bool:
        """Migrate push subscriptions from JSON to Supabase."""
        try:
            logger.info("Starting push subscriptions migration...")
            
            # Load push subscriptions - try both possible paths
            push_paths = [
                Path('data/push_subscriptions.json'),
                Path('app/data/push_subscriptions.json'),
                Path(Config.PUSH_SUBSCRIPTIONS_JSON_PATH)
            ]
            
            subscriptions = None
            push_path = None
            
            for path in push_paths:
                if path.exists():
                    push_path = path
                    with open(path, 'r') as f:
                        subscriptions = json.load(f)
                    break
            
            if subscriptions is None:
                logger.warning("No push subscriptions file found")
                return True
            
            logger.info(f"Loaded {len(subscriptions)} push subscriptions from {push_path}")
            
            if not subscriptions:
                logger.info("No push subscriptions to migrate")
                return True
            
            # Check if data already exists
            existing_result = self.supabase.table('push_subscriptions').select('*').limit(1).execute()
            if existing_result.data:
                logger.info("Push subscriptions table already contains data, skipping migration")
                return True
            
            # Migrate subscriptions
            for subscription in subscriptions:
                record = {
                    'endpoint': subscription.get('endpoint', ''),
                    'p256dh_key': subscription.get('keys', {}).get('p256dh', ''),
                    'auth_key': subscription.get('keys', {}).get('auth', ''),
                    'is_active': True
                }
                
                result = self.supabase.table('push_subscriptions').upsert(record, on_conflict='endpoint').execute()
                logger.debug(f"Migrated subscription: {subscription.get('endpoint', 'Unknown')[:50]}...")
            
            logger.info("Push subscriptions migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate push subscriptions: {e}")
            return False

    def verify_migration(self) -> bool:
        """Verify that data was migrated successfully."""
        try:
            logger.info("Verifying migration...")
            
            # Check audit logs
            audit_result = self.supabase.table('audit_logs').select('*', count='exact').execute()
            audit_count = audit_result.count
            logger.info(f"Audit logs table contains {audit_count} records")
            
            # Check push subscriptions
            push_result = self.supabase.table('push_subscriptions').select('*').execute()
            push_count = len(push_result.data)
            logger.info(f"Push subscriptions table contains {push_count} records")
            
            logger.info("Migration verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to verify migration: {e}")
            return False

def main():
    """Main migration function."""
    logger.info("=== ALORF BIOMED Data Migration Started ===")
    
    try:
        migration = DataMigration()
        
        # Step 1: Migrate audit logs
        logger.info("Step 1: Migrating audit logs...")
        if not migration.migrate_audit_logs():
            logger.error("Failed to migrate audit logs")
            return False
        
        # Step 2: Migrate push subscriptions
        logger.info("Step 2: Migrating push subscriptions...")
        if not migration.migrate_push_subscriptions():
            logger.error("Failed to migrate push subscriptions")
            return False
        
        # Step 3: Verify migration
        logger.info("Step 3: Verifying migration...")
        if not migration.verify_migration():
            logger.error("Migration verification failed")
            return False
        
        logger.info("=== ALORF BIOMED Data Migration Completed Successfully ===")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
