"""
Supabase-based Settings Service
Replaces JSON file-based settings with Supabase database storage
"""

import logging
import json
from typing import Dict, Any, List, Optional
from supabase import create_client, Client

from app.config import Config

logger = logging.getLogger(__name__)

class SupabaseSettingsService:
    """Service for managing application settings using Supabase database."""
    
    def __init__(self):
        """Initialize the Supabase client."""
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.debug("SupabaseSettingsService initialized")

    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default settings structure."""
        return {
            "email_notifications_enabled": True,
            "email_reminder_interval_minutes": 60,
            "recipient_email": "",
            "push_notifications_enabled": False,
            "push_notification_interval_minutes": 60,
            "email_send_time_hour": 7,
            "reminder_timing": {
                "60_days_before": False,
                "14_days_before": False,
                "1_day_before": False
            },
            "scheduler_interval_hours": 24,
            "enable_automatic_reminders": True,
            "cc_emails": "",
            "automatic_backup_enabled": True,
            "automatic_backup_interval_hours": 8,
            "use_daily_send_time": True,
            "use_legacy_interval": False,
            "email_send_time": "07:35"
        }

    def load_settings(self) -> Dict[str, Any]:
        """Load settings from Supabase database."""
        try:
            logger.debug("Loading settings from Supabase")
            
            # Get all settings from the database
            result = self.supabase.table('settings').select('*').execute()
            
            if not result.data:
                logger.info("No settings found in database, returning defaults")
                return self._get_default_settings()
            
            # Convert settings from key-value pairs to dictionary
            settings = {}
            for setting in result.data:
                key = setting['key']
                value = setting['value']
                settings[key] = value
            
            # Merge with defaults to ensure all required keys exist
            default_settings = self._get_default_settings()
            final_settings = default_settings.copy()
            final_settings.update(settings)
            
            logger.info(f"Successfully loaded {len(settings)} settings from database")
            return final_settings
            
        except Exception as e:
            logger.error(f"Failed to load settings from database: {e}")
            logger.info("Returning default settings due to error")
            return self._get_default_settings()

    def save_settings(self, settings_data: Dict[str, Any]) -> bool:
        """Save settings to Supabase database."""
        try:
            logger.info(f"Saving {len(settings_data)} settings to database")
            
            # Prepare settings for upsert
            settings_records = []
            for key, value in settings_data.items():
                # Determine category based on key
                category = 'general'
                if 'email' in key.lower():
                    category = 'email'
                elif 'push' in key.lower() or 'notification' in key.lower():
                    category = 'notifications'
                elif 'backup' in key.lower():
                    category = 'backup'
                elif 'scheduler' in key.lower() or 'reminder' in key.lower():
                    category = 'scheduler'
                
                record = {
                    'key': key,
                    'value': value,
                    'category': category,
                    'description': f'Application setting: {key}'
                }
                settings_records.append(record)
            
            # Upsert all settings
            result = self.supabase.table('settings').upsert(settings_records, on_conflict='key').execute()
            
            logger.info("Settings saved successfully to database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save settings to database: {e}")
            return False

    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific setting value."""
        try:
            result = self.supabase.table('settings').select('value').eq('key', key).execute()
            
            if result.data:
                return result.data[0]['value']
            else:
                logger.debug(f"Setting '{key}' not found, returning default: {default}")
                return default
                
        except Exception as e:
            logger.error(f"Failed to get setting '{key}': {e}")
            return default

    def set_setting(self, key: str, value: Any, category: str = 'general') -> bool:
        """Set a specific setting value."""
        try:
            record = {
                'key': key,
                'value': value,
                'category': category,
                'description': f'Application setting: {key}'
            }
            
            result = self.supabase.table('settings').upsert(record, on_conflict='key').execute()
            logger.debug(f"Setting '{key}' updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set setting '{key}': {e}")
            return False

    def delete_setting(self, key: str) -> bool:
        """Delete a specific setting."""
        try:
            result = self.supabase.table('settings').delete().eq('key', key).execute()
            logger.debug(f"Setting '{key}' deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete setting '{key}': {e}")
            return False

    def get_settings_by_category(self, category: str) -> Dict[str, Any]:
        """Get all settings in a specific category."""
        try:
            result = self.supabase.table('settings').select('*').eq('category', category).execute()
            
            settings = {}
            for setting in result.data:
                settings[setting['key']] = setting['value']
            
            return settings
            
        except Exception as e:
            logger.error(f"Failed to get settings for category '{category}': {e}")
            return {}

    def load_users(self) -> List[Dict[str, Any]]:
        """Load users from Supabase database."""
        try:
            result = self.supabase.table('users').select('*').execute()
            
            users = []
            for user in result.data:
                users.append({
                    'username': user['username'],
                    'password': user['password_hash'],  # Keep the same key for compatibility
                    'role': user['role']
                })
            
            logger.info(f"Loaded {len(users)} users from database")
            return users
            
        except Exception as e:
            logger.error(f"Failed to load users from database: {e}")
            return []

    def save_users(self, users: List[Dict[str, Any]]) -> bool:
        """Save users to Supabase database."""
        try:
            # Prepare user records
            user_records = []
            for user in users:
                record = {
                    'username': user['username'],
                    'password_hash': user['password'],  # Map from 'password' key
                    'role': user['role'],
                    'is_active': True
                }
                user_records.append(record)
            
            # Upsert users
            result = self.supabase.table('users').upsert(user_records, on_conflict='username').execute()
            
            logger.info(f"Saved {len(users)} users to database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save users to database: {e}")
            return False

    def load_roles(self) -> Dict[str, Dict[str, Any]]:
        """Load roles from Supabase database."""
        try:
            result = self.supabase.table('roles').select('*').execute()
            
            roles = {}
            for role in result.data:
                roles[role['name']] = {
                    'permissions': role['permissions']
                }
            
            logger.info(f"Loaded {len(roles)} roles from database")
            return roles
            
        except Exception as e:
            logger.error(f"Failed to load roles from database: {e}")
            return {}

    def save_roles(self, roles: Dict[str, Dict[str, Any]]) -> bool:
        """Save roles to Supabase database."""
        try:
            # Prepare role records
            role_records = []
            for role_name, role_info in roles.items():
                record = {
                    'name': role_name,
                    'permissions': role_info.get('permissions', []),
                    'description': f'{role_name} role permissions'
                }
                role_records.append(record)
            
            # Upsert roles
            result = self.supabase.table('roles').upsert(role_records, on_conflict='name').execute()
            
            logger.info(f"Saved {len(roles)} roles to database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save roles to database: {e}")
            return False

    def migrate_from_json(self, json_settings: Dict[str, Any]) -> bool:
        """Migrate settings from JSON format to Supabase."""
        try:
            logger.info("Migrating settings from JSON to Supabase")
            
            # Separate users and roles from other settings
            users_data = json_settings.pop('users', [])
            roles_data = json_settings.pop('roles', {})
            
            # Save users and roles
            if users_data:
                self.save_users(users_data)
            
            if roles_data:
                self.save_roles(roles_data)
            
            # Save remaining settings
            if json_settings:
                self.save_settings(json_settings)
            
            logger.info("JSON to Supabase migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate settings from JSON: {e}")
            return False


# Global instance for backward compatibility
_settings_service = None

def get_settings_service() -> SupabaseSettingsService:
    """Get the global settings service instance."""
    global _settings_service
    if _settings_service is None:
        _settings_service = SupabaseSettingsService()
    return _settings_service
