#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to get comprehensive information about all tables in the ALORF BIOMED Supabase database.
This script will show:
1. All table names
2. Table schemas (columns, data types)
3. Record counts for each table
4. Sample data from each table
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
from supabase import create_client, Client

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseAnalyzer:
    """Analyze the ALORF BIOMED Supabase database."""
    
    def __init__(self):
        """Initialize the database analyzer."""
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_service_key = os.getenv('SUPABASE_SERVICE_KEY')
        
        if not self.supabase_url or not self.supabase_service_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_service_key)
        logger.info("Connected to Supabase database")
    
    def get_all_tables(self) -> List[str]:
        """Get all table names in the database."""
        try:
            # Query the information_schema to get all tables
            query = """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name;
            """
            
            result = self.supabase.rpc('execute_sql', {'query': query}).execute()
            
            if result.data:
                return [row['table_name'] for row in result.data]
            else:
                # Fallback: try to query known tables
                known_tables = [
                    'settings', 'users', 'roles', 'audit_logs', 'push_subscriptions',
                    'ppm_equipment', 'ocm_equipment', 'ppm_quarters',
                    'training_records', 'machine_trainer_assignments',
                    'departments', 'trainers', 'equipment_history'
                ]
                
                existing_tables = []
                for table in known_tables:
                    try:
                        self.supabase.table(table).select('*').limit(1).execute()
                        existing_tables.append(table)
                    except Exception:
                        pass
                
                return existing_tables
                
        except Exception as e:
            logger.error(f"Error getting table names: {e}")
            # Return known tables as fallback
            return [
                'settings', 'users', 'roles', 'audit_logs', 'push_subscriptions',
                'ppm_equipment', 'ocm_equipment', 'ppm_quarters',
                'training_records', 'machine_trainer_assignments',
                'departments', 'trainers', 'equipment_history'
            ]
    
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """Get the schema (columns) for a specific table."""
        try:
            query = f"""
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = '{table_name}'
            ORDER BY ordinal_position;
            """
            
            result = self.supabase.rpc('execute_sql', {'query': query}).execute()
            
            if result.data:
                return result.data
            else:
                return []
                
        except Exception as e:
            logger.error(f"Error getting schema for table {table_name}: {e}")
            return []
    
    def get_table_count(self, table_name: str) -> int:
        """Get the number of records in a table."""
        try:
            result = self.supabase.table(table_name).select('*', count='exact').execute()
            return result.count if result.count is not None else 0
        except Exception as e:
            logger.error(f"Error getting count for table {table_name}: {e}")
            return 0
    
    def get_sample_data(self, table_name: str, limit: int = 3) -> List[Dict[str, Any]]:
        """Get sample data from a table."""
        try:
            result = self.supabase.table(table_name).select('*').limit(limit).execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Error getting sample data for table {table_name}: {e}")
            return []
    
    def analyze_database(self) -> Dict[str, Any]:
        """Perform comprehensive database analysis."""
        logger.info("Starting comprehensive database analysis...")
        
        # Get all tables
        tables = self.get_all_tables()
        logger.info(f"Found {len(tables)} tables: {', '.join(tables)}")
        
        analysis = {
            'total_tables': len(tables),
            'tables': {}
        }
        
        total_records = 0
        
        for table_name in tables:
            logger.info(f"Analyzing table: {table_name}")
            
            # Get table information
            schema = self.get_table_schema(table_name)
            count = self.get_table_count(table_name)
            sample_data = self.get_sample_data(table_name)
            
            total_records += count
            
            analysis['tables'][table_name] = {
                'record_count': count,
                'schema': schema,
                'sample_data': sample_data
            }
        
        analysis['total_records'] = total_records
        
        logger.info(f"Database analysis complete. Total records: {total_records}")
        return analysis
    
    def print_analysis(self, analysis: Dict[str, Any]):
        """Print the database analysis in a formatted way."""
        print("\n" + "="*80)
        print("🗄️  ALORF BIOMED SUPABASE DATABASE ANALYSIS")
        print("="*80)
        
        print(f"\n📊 SUMMARY:")
        print(f"   • Total Tables: {analysis['total_tables']}")
        print(f"   • Total Records: {analysis['total_records']:,}")
        
        print(f"\n📋 TABLE DETAILS:")
        print("-" * 80)
        
        for table_name, table_info in analysis['tables'].items():
            count = table_info['record_count']
            schema = table_info['schema']
            
            print(f"\n🔹 TABLE: {table_name.upper()}")
            print(f"   📈 Records: {count:,}")
            
            if schema:
                print(f"   📝 Schema ({len(schema)} columns):")
                for col in schema:
                    nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                    data_type = col['data_type']
                    if col['character_maximum_length']:
                        data_type += f"({col['character_maximum_length']})"
                    
                    print(f"      • {col['column_name']}: {data_type} {nullable}")
            
            # Show sample data if available
            sample_data = table_info['sample_data']
            if sample_data and count > 0:
                print(f"   📄 Sample Data (first {len(sample_data)} records):")
                for i, record in enumerate(sample_data, 1):
                    print(f"      Record {i}: {dict(list(record.items())[:3])}...")  # Show first 3 fields
        
        print("\n" + "="*80)

def main():
    """Main function to run the database analysis."""
    try:
        analyzer = DatabaseAnalyzer()
        analysis = analyzer.analyze_database()
        analyzer.print_analysis(analysis)
        
    except Exception as e:
        logger.error(f"Error during database analysis: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
