#!/usr/bin/env python3
"""
Simple test script for DepartmentService to verify both modes work.
"""
import os
import sys
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_json_mode():
    """Test JSON file mode."""
    logger.info("🧪 Testing JSON File Mode")
    
    # Set environment for JSON mode
    os.environ['USE_SUPABASE'] = 'false'
    
    try:
        from app.services.department_service import DepartmentService
        service = DepartmentService()
        
        logger.info(f"✅ Service initialized (database mode: {service.use_database})")
        
        # Test create
        dept_data = {'department_name': 'Test Dept JSON', 'information': 'Test info'}
        created = service.create(dept_data)
        assert created is not None
        logger.info(f"✅ Created department: {created['department_name']}")
        
        # Test get all
        all_depts = service.get_all()
        assert len(all_depts) > 0
        logger.info(f"✅ Found {len(all_depts)} departments")
        
        # Test get by ID
        found = service.get_by_id(created['id'])
        assert found is not None
        assert found['department_name'] == 'Test Dept JSON'
        logger.info(f"✅ Found department by ID: {found['department_name']}")
        
        # Test update
        updated = service.update(created['id'], {'department_name': 'Updated Test Dept JSON'})
        assert updated is not None
        assert updated['department_name'] == 'Updated Test Dept JSON'
        logger.info(f"✅ Updated department: {updated['department_name']}")
        
        # Test delete
        deleted = service.delete(created['id'])
        assert deleted is True
        logger.info("✅ Deleted department")
        
        logger.info("🎉 JSON File Mode: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ JSON File Mode failed: {e}")
        return False

def test_database_mode():
    """Test database mode."""
    logger.info("🧪 Testing Database Mode")
    
    # Check if database is configured
    if not os.getenv('DATABASE_URL'):
        logger.warning("⚠️  Skipping database tests - DATABASE_URL not set")
        return True
    
    # Set environment for database mode
    os.environ['USE_SUPABASE'] = 'true'
    
    try:
        from app.services.department_service import DepartmentService
        service = DepartmentService()
        
        logger.info(f"✅ Service initialized (database mode: {service.use_database})")
        
        # Test create
        dept_data = {'department_name': 'Test Dept DB', 'information': 'Test info DB'}
        created = service.create(dept_data)
        assert created is not None
        logger.info(f"✅ Created department: {created['department_name']}")
        
        # Test get all
        all_depts = service.get_all()
        assert len(all_depts) > 0
        logger.info(f"✅ Found {len(all_depts)} departments")
        
        # Test get by ID
        found = service.get_by_id(created['id'])
        assert found is not None
        assert found['department_name'] == 'Test Dept DB'
        logger.info(f"✅ Found department by ID: {found['department_name']}")
        
        # Test update
        updated = service.update(created['id'], {'department_name': 'Updated Test Dept DB'})
        assert updated is not None
        assert updated['department_name'] == 'Updated Test Dept DB'
        logger.info(f"✅ Updated department: {updated['department_name']}")
        
        # Test delete
        deleted = service.delete(created['id'])
        assert deleted is True
        logger.info("✅ Deleted department")
        
        logger.info("🎉 Database Mode: ALL TESTS PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database Mode failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("🚀 DepartmentService Simple Test Suite")
    logger.info("=" * 50)
    
    # Test JSON mode
    json_success = test_json_mode()
    
    print()  # Add spacing
    
    # Test database mode
    db_success = test_database_mode()
    
    # Summary
    logger.info("=" * 50)
    logger.info("📊 FINAL RESULTS")
    logger.info(f"JSON File Mode: {'✅ PASSED' if json_success else '❌ FAILED'}")
    logger.info(f"Database Mode: {'✅ PASSED' if db_success else '❌ FAILED'}")
    
    overall_success = json_success and db_success
    if overall_success:
        logger.info("🎉 All tests passed! DepartmentService is ready.")
    else:
        logger.error("❌ Some tests failed.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
