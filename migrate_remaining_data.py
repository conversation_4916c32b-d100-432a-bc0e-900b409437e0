#!/usr/bin/env python3
"""
Efficient batch migration for remaining large datasets.
Migrates PPM, OCM, and Training data with batch processing.
"""
import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_connection():
    """Get database connection."""
    from sqlalchemy import create_engine

    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        raise ValueError("DATABASE_URL not set")

    engine = create_engine(database_url, pool_pre_ping=True)
    return engine

def migrate_ppm_batch(batch_size=50):
    """Migrate PPM equipment in batches."""
    logger.info("🔧 Starting PPM Equipment batch migration...")

    # Load PPM data
    with open('data/ppm.json', 'r', encoding='utf-8') as f:
        ppm_data = json.load(f)

    logger.info(f"📁 Loaded {len(ppm_data)} PPM records")

    engine = get_db_connection()
    migrated_count = 0
    quarter_count = 0

    from sqlalchemy import text

    with engine.connect() as conn:
        # Process in batches
        for i in range(0, len(ppm_data), batch_size):
            batch = ppm_data[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: records {i+1}-{min(i+batch_size, len(ppm_data))}")
            
            for ppm in batch:
                try:
                    # Insert PPM equipment
                    ppm_sql = text("""
                        INSERT INTO ppm_equipment (no, department, name, model, serial, manufacturer, 
                                                 log_number, installation_date, warranty_end, status, has_history)
                        VALUES (:no, :department, :name, :model, :serial, :manufacturer, 
                               :log_number, :installation_date, :warranty_end, :status, :has_history)
                        ON CONFLICT (serial) DO NOTHING
                        RETURNING id
                    """)
                    
                    result = conn.execute(ppm_sql, {
                        'no': ppm.get('NO'),
                        'department': ppm.get('Department'),
                        'name': ppm.get('Name'),
                        'model': ppm.get('MODEL'),
                        'serial': ppm.get('SERIAL'),
                        'manufacturer': ppm.get('MANUFACTURER'),
                        'log_number': ppm.get('LOG_Number'),
                        'installation_date': ppm.get('Installation_Date'),
                        'warranty_end': ppm.get('Warranty_End'),
                        'status': ppm.get('Status', 'Upcoming'),
                        'has_history': ppm.get('has_history', False)
                    })
                    
                    row = result.fetchone()
                    if row:
                        equipment_id = row[0]
                        migrated_count += 1
                        
                        # Insert quarterly data
                        quarters = ['PPM_Q_I', 'PPM_Q_II', 'PPM_Q_III', 'PPM_Q_IV']
                        quarter_names = ['Q_I', 'Q_II', 'Q_III', 'Q_IV']
                        
                        for quarter_key, quarter_name in zip(quarters, quarter_names):
                            quarter_data = ppm.get(quarter_key, {})
                            if quarter_data and isinstance(quarter_data, dict):
                                quarter_sql = text("""
                                    INSERT INTO ppm_quarters (ppm_equipment_id, quarter, engineer, quarter_date, status)
                                    VALUES (:equipment_id, :quarter, :engineer, :quarter_date, :status)
                                """)
                                
                                conn.execute(quarter_sql, {
                                    'equipment_id': equipment_id,
                                    'quarter': quarter_name,
                                    'engineer': quarter_data.get('engineer'),
                                    'quarter_date': quarter_data.get('quarter_date'),
                                    'status': quarter_data.get('status')
                                })
                                quarter_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate PPM {ppm.get('SERIAL', 'unknown')}: {e}")
            
            # Commit batch
            conn.commit()
            logger.info(f"✅ Batch {i//batch_size + 1} committed")
    
    logger.info(f"✅ PPM migration completed: {migrated_count} equipment, {quarter_count} quarters")
    return migrated_count, quarter_count

def migrate_ocm_batch(batch_size=50):
    """Migrate OCM equipment in batches."""
    logger.info("🔧 Starting OCM Equipment batch migration...")

    # Load OCM data
    with open('data/ocm.json', 'r', encoding='utf-8') as f:
        ocm_data = json.load(f)

    logger.info(f"📁 Loaded {len(ocm_data)} OCM records")

    engine = get_db_connection()
    migrated_count = 0

    from sqlalchemy import text

    with engine.connect() as conn:
        # Process in batches
        for i in range(0, len(ocm_data), batch_size):
            batch = ocm_data[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: records {i+1}-{min(i+batch_size, len(ocm_data))}")
            
            for ocm in batch:
                try:
                    ocm_sql = text("""
                        INSERT INTO ocm_equipment (no, department, name, model, serial, manufacturer, 
                                                 log_number, installation_date, warranty_end, service_date,
                                                 engineer, next_maintenance, status, has_history)
                        VALUES (:no, :department, :name, :model, :serial, :manufacturer, 
                               :log_number, :installation_date, :warranty_end, :service_date,
                               :engineer, :next_maintenance, :status, :has_history)
                        ON CONFLICT (serial) DO NOTHING
                    """)
                    
                    conn.execute(ocm_sql, {
                        'no': ocm.get('NO'),
                        'department': ocm.get('Department'),
                        'name': ocm.get('Name'),
                        'model': ocm.get('Model'),
                        'serial': ocm.get('Serial'),
                        'manufacturer': ocm.get('Manufacturer'),
                        'log_number': ocm.get('Log_Number'),
                        'installation_date': ocm.get('Installation_Date'),
                        'warranty_end': ocm.get('Warranty_End'),
                        'service_date': ocm.get('Service_Date'),
                        'engineer': ocm.get('Engineer'),
                        'next_maintenance': ocm.get('Next_Maintenance'),
                        'status': ocm.get('Status', 'Upcoming'),
                        'has_history': ocm.get('has_history', False)
                    })
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate OCM {ocm.get('Serial', 'unknown')}: {e}")
            
            # Commit batch
            conn.commit()
            logger.info(f"✅ Batch {i//batch_size + 1} committed")
    
    logger.info(f"✅ OCM migration completed: {migrated_count} equipment")
    return migrated_count

def migrate_training_batch(batch_size=50):
    """Migrate training records in batches."""
    logger.info("📚 Starting Training Records batch migration...")

    # Load training data
    with open('data/training.json', 'r', encoding='utf-8') as f:
        training_data = json.load(f)

    logger.info(f"📁 Loaded {len(training_data)} training records")

    engine = get_db_connection()
    migrated_count = 0
    assignment_count = 0

    from sqlalchemy import text

    with engine.connect() as conn:
        # Process in batches
        for i in range(0, len(training_data), batch_size):
            batch = training_data[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: records {i+1}-{min(i+batch_size, len(training_data))}")
            
            for training in batch:
                try:
                    # Insert training record
                    training_sql = text("""
                        INSERT INTO training_records (employee_id, name, department, last_trained_date, next_due_date)
                        VALUES (:employee_id, :name, :department, :last_trained_date, :next_due_date)
                        RETURNING id
                    """)
                    
                    result = conn.execute(training_sql, {
                        'employee_id': training.get('employee_id'),
                        'name': training.get('name'),
                        'department': training.get('department'),
                        'last_trained_date': training.get('last_trained_date'),
                        'next_due_date': training.get('next_due_date')
                    })
                    
                    row = result.fetchone()
                    if row:
                        training_id = row[0]
                        migrated_count += 1
                        
                        # Insert machine trainer assignments
                        assignments = training.get('machine_trainer_assignments', [])
                        for assignment in assignments:
                            if assignment and isinstance(assignment, dict):
                                assignment_sql = text("""
                                    INSERT INTO machine_trainer_assignments (training_record_id, machine, trainer)
                                    VALUES (:training_id, :machine, :trainer)
                                """)
                                
                                conn.execute(assignment_sql, {
                                    'training_id': training_id,
                                    'machine': assignment.get('machine'),
                                    'trainer': assignment.get('trainer')
                                })
                                assignment_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate training record: {e}")
            
            # Commit batch
            conn.commit()
            logger.info(f"✅ Batch {i//batch_size + 1} committed")
    
    logger.info(f"✅ Training migration completed: {migrated_count} records, {assignment_count} assignments")
    return migrated_count, assignment_count

def verify_migration():
    """Verify migration results."""
    logger.info("🔍 Verifying migration results...")
    
    engine = get_db_connection()
    
    with engine.connect() as conn:
        from sqlalchemy import text
        
        # Check record counts
        tables = [
            'departments', 'trainers', 'ppm_equipment', 'ppm_quarters',
            'ocm_equipment', 'training_records', 'machine_trainer_assignments'
        ]
        
        for table in tables:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
            count = result.fetchone()[0]
            logger.info(f"✅ {table}: {count} records")

def main():
    """Main migration function."""
    logger.info("🚀 ALORF BIOMED Remaining Data Migration")
    logger.info("=" * 60)
    
    try:
        # Migrate PPM equipment
        ppm_count, quarter_count = migrate_ppm_batch()
        
        # Migrate OCM equipment  
        ocm_count = migrate_ocm_batch()
        
        # Migrate training records
        training_count, assignment_count = migrate_training_batch()
        
        # Verify results
        verify_migration()
        
        # Summary
        logger.info("=" * 60)
        logger.info("📊 MIGRATION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"PPM Equipment: {ppm_count} records")
        logger.info(f"PPM Quarters: {quarter_count} records")
        logger.info(f"OCM Equipment: {ocm_count} records")
        logger.info(f"Training Records: {training_count} records")
        logger.info(f"Machine Assignments: {assignment_count} records")
        logger.info("=" * 60)
        logger.info("🎉 Migration completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
