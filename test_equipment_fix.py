#!/usr/bin/env python3
"""
Test the fixed equipment data loading from Supabase
"""

import os
import sys
import logging

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_supabase_equipment_service():
    """Test the new SupabaseEquipmentService."""
    try:
        logger.info("=== Testing SupabaseEquipmentService ===")
        
        from app.services.supabase_equipment_service import get_equipment_service
        
        equipment_service = get_equipment_service()
        
        # Test PPM data loading
        logger.info("Testing PPM data loading...")
        ppm_data = equipment_service.load_equipment_data('ppm')
        logger.info(f"PPM data loaded: {len(ppm_data)} records")
        
        if ppm_data:
            sample_ppm = ppm_data[0]
            logger.info(f"Sample PPM record keys: {list(sample_ppm.keys())}")
            logger.info(f"Sample PPM SERIAL: {sample_ppm.get('SERIAL', 'N/A')}")
            logger.info(f"Sample PPM EQUIPMENT: {sample_ppm.get('EQUIPMENT', 'N/A')}")
        
        # Test OCM data loading
        logger.info("Testing OCM data loading...")
        ocm_data = equipment_service.load_equipment_data('ocm')
        logger.info(f"OCM data loaded: {len(ocm_data)} records")
        
        if ocm_data:
            sample_ocm = ocm_data[0]
            logger.info(f"Sample OCM record keys: {list(sample_ocm.keys())}")
            logger.info(f"Sample OCM Serial: {sample_ocm.get('Serial', 'N/A')}")
            logger.info(f"Sample OCM EQUIPMENT: {sample_ocm.get('EQUIPMENT', 'N/A')}")
        
        return len(ppm_data) > 0 and len(ocm_data) > 0
        
    except Exception as e:
        logger.error(f"Error testing SupabaseEquipmentService: {e}")
        return False

def test_data_service_integration():
    """Test DataService integration with Supabase."""
    try:
        logger.info("=== Testing DataService Integration ===")
        
        from app.services.data_service import DataService
        
        # Test PPM data loading through DataService
        logger.info("Testing DataService PPM loading...")
        ppm_data = DataService.load_data('ppm')
        logger.info(f"DataService PPM data: {len(ppm_data)} records")
        
        # Test OCM data loading through DataService
        logger.info("Testing DataService OCM loading...")
        ocm_data = DataService.load_data('ocm')
        logger.info(f"DataService OCM data: {len(ocm_data)} records")
        
        # Test get_all_entries method
        logger.info("Testing get_all_entries...")
        all_ppm = DataService.get_all_entries('ppm')
        logger.info(f"get_all_entries PPM: {len(all_ppm)} records")
        
        all_ocm = DataService.get_all_entries('ocm')
        logger.info(f"get_all_entries OCM: {len(all_ocm)} records")
        
        return len(ppm_data) > 0 and len(ocm_data) > 0
        
    except Exception as e:
        logger.error(f"Error testing DataService integration: {e}")
        return False

def test_api_endpoints():
    """Test the API endpoints with the new data loading."""
    try:
        logger.info("=== Testing API Endpoints ===")
        
        import requests
        
        # Test PPM API endpoint
        try:
            response = requests.get('http://localhost:5001/api/equipment/ppm', timeout=10)
            logger.info(f"PPM API endpoint: Status {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"PPM API returned: {len(data)} records")
                if data:
                    logger.info(f"Sample PPM API record keys: {list(data[0].keys())}")
        except Exception as e:
            logger.warning(f"PPM API endpoint error: {e}")
        
        # Test OCM API endpoint
        try:
            response = requests.get('http://localhost:5001/api/equipment/ocm', timeout=10)
            logger.info(f"OCM API endpoint: Status {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"OCM API returned: {len(data)} records")
                if data:
                    logger.info(f"Sample OCM API record keys: {list(data[0].keys())}")
        except Exception as e:
            logger.warning(f"OCM API endpoint error: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing API endpoints: {e}")
        return False

def test_equipment_counts():
    """Test equipment count functionality."""
    try:
        logger.info("=== Testing Equipment Counts ===")
        
        from app.services.supabase_equipment_service import get_equipment_service
        
        equipment_service = get_equipment_service()
        
        # Test counts
        ppm_count = equipment_service.get_equipment_count('ppm')
        logger.info(f"PPM equipment count: {ppm_count}")
        
        ocm_count = equipment_service.get_equipment_count('ocm')
        logger.info(f"OCM equipment count: {ocm_count}")
        
        return ppm_count > 0 and ocm_count > 0
        
    except Exception as e:
        logger.error(f"Error testing equipment counts: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=== ALORF BIOMED Equipment Fix Test Started ===")
    
    tests = [
        ("SupabaseEquipmentService", test_supabase_equipment_service),
        ("DataService Integration", test_data_service_integration),
        ("Equipment Counts", test_equipment_counts),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n=== Test Results Summary ===")
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\nTotal: {len(results)} tests, {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All Equipment Tests Passed! Equipment data should now be visible!")
    else:
        logger.error(f"❌ {failed} Tests Failed! Equipment issues remain.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
