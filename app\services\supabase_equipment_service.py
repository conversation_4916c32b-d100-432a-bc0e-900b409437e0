"""
Supabase-based Equipment Service
Loads equipment data from Supabase database instead of JSON files
"""

import logging
from typing import List, Dict, Any, Optional, Literal
from supabase import create_client, Client

from app.config import Config

logger = logging.getLogger(__name__)

class SupabaseEquipmentService:
    """Service for managing equipment data using Supabase database."""
    
    def __init__(self):
        """Initialize the Supabase client."""
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        self.supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.debug("SupabaseEquipmentService initialized")

    def get_table_name(self, data_type: str) -> str:
        """Get the correct table name for the data type."""
        if data_type == 'ppm':
            return 'ppm_equipment'
        elif data_type == 'ocm':
            return 'ocm_equipment'
        else:
            raise ValueError(f"Invalid data type: {data_type}")

    def load_equipment_data(self, data_type: Literal['ppm', 'ocm']) -> List[Dict[str, Any]]:
        """
        Load equipment data from Supabase database.
        
        Args:
            data_type: Type of equipment data to load ('ppm' or 'ocm')
            
        Returns:
            List of equipment records
        """
        try:
            table_name = self.get_table_name(data_type)
            
            # Get all equipment data from Supabase
            result = self.supabase.table(table_name).select('*').execute()
            
            equipment_data = result.data
            logger.info(f"Loaded {len(equipment_data)} {data_type} equipment records from Supabase")
            
            # Convert Supabase data to format expected by the application
            converted_data = []
            for equipment in equipment_data:
                # Convert the data format to match what the application expects
                converted_equipment = self._convert_supabase_to_app_format(equipment, data_type)
                converted_data.append(converted_equipment)
            
            return converted_data
            
        except Exception as e:
            logger.error(f"Failed to load {data_type} equipment data from Supabase: {e}")
            return []

    def _convert_supabase_to_app_format(self, equipment: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        Convert Supabase equipment data to the format expected by the application.
        
        Args:
            equipment: Equipment data from Supabase
            data_type: Type of equipment ('ppm' or 'ocm')
            
        Returns:
            Equipment data in application format
        """
        try:
            # Base conversion - copy all fields
            converted = equipment.copy()
            
            # Ensure required fields exist
            if data_type == 'ppm':
                # PPM equipment uses 'SERIAL' field - map from 'serial' field in Supabase
                if 'serial' in converted and 'SERIAL' not in converted:
                    converted['SERIAL'] = converted['serial']
                elif 'serial_number' in converted and 'SERIAL' not in converted:
                    converted['SERIAL'] = converted['serial_number']

                # Ensure NO field exists (for sorting)
                if 'NO' not in converted:
                    converted['NO'] = converted.get('no', converted.get('id', 0))

                # Ensure Status field exists
                if 'Status' not in converted:
                    converted['Status'] = converted.get('status', 'Active')

                # Map common fields - use exact field names expected by PPM template
                field_mapping = {
                    'name': 'Name',  # Equipment name
                    'model': 'MODEL',
                    'department': 'Department',
                    'manufacturer': 'MANUFACTURER',
                    'log_number': 'LOG_Number',
                    'installation_date': 'Installation_Date',
                    'warranty_end': 'Warranty_End'
                }
                
            else:  # OCM
                # OCM equipment uses 'Serial' field - map from 'serial' field in Supabase
                if 'serial' in converted and 'Serial' not in converted:
                    converted['Serial'] = converted['serial']
                elif 'serial_number' in converted and 'Serial' not in converted:
                    converted['Serial'] = converted['serial_number']

                # Ensure NO field exists (for sorting)
                if 'NO' not in converted:
                    converted['NO'] = converted.get('no', converted.get('id', 0))

                # Ensure Status field exists
                if 'Status' not in converted:
                    converted['Status'] = converted.get('status', 'Active')

                # Map common fields for OCM - use exact field names expected by OCM template
                field_mapping = {
                    'name': 'Name',  # Equipment name
                    'model': 'MODEL',
                    'department': 'Department',
                    'manufacturer': 'MANUFACTURER',
                    'log_number': 'LOG_Number',
                    'installation_date': 'Installation_Date',
                    'warranty_end': 'Warranty_End',
                    'service_date': 'Service_Date',
                    'engineer': 'Engineer',
                    'next_maintenance': 'Next_Maintenance'
                }
            
            # Apply field mapping
            for supabase_field, app_field in field_mapping.items():
                if supabase_field in converted and app_field not in converted:
                    converted[app_field] = converted[supabase_field]

            # Add PPM quarterly data structure if this is PPM equipment
            if data_type == 'ppm':
                # Create quarterly data structure expected by PPM templates
                for quarter in ['PPM_Q_I', 'PPM_Q_II', 'PPM_Q_III', 'PPM_Q_IV']:
                    if quarter not in converted:
                        converted[quarter] = {
                            'engineer': '',
                            'quarter_date': '',
                            'status': 'N/A',
                            'status_class': 'secondary'
                        }

            # Add history flag (will be updated later by DataService)
            converted['has_history'] = False

            return converted
            
        except Exception as e:
            logger.error(f"Error converting equipment data: {e}")
            return equipment

    def get_equipment_count(self, data_type: Literal['ppm', 'ocm']) -> int:
        """
        Get the count of equipment records.
        
        Args:
            data_type: Type of equipment data ('ppm' or 'ocm')
            
        Returns:
            Number of equipment records
        """
        try:
            table_name = self.get_table_name(data_type)
            
            result = self.supabase.table(table_name).select('*', count='exact').execute()
            count = result.count
            
            logger.debug(f"Found {count} {data_type} equipment records in Supabase")
            return count
            
        except Exception as e:
            logger.error(f"Failed to get {data_type} equipment count from Supabase: {e}")
            return 0

    def get_equipment_by_serial(self, data_type: Literal['ppm', 'ocm'], serial: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific equipment record by serial number.
        
        Args:
            data_type: Type of equipment data ('ppm' or 'ocm')
            serial: Serial number to search for
            
        Returns:
            Equipment record if found, None otherwise
        """
        try:
            table_name = self.get_table_name(data_type)
            
            # Try searching by serial_number field
            result = self.supabase.table(table_name).select('*').eq('serial_number', serial).execute()
            
            if result.data:
                equipment = result.data[0]
                converted_equipment = self._convert_supabase_to_app_format(equipment, data_type)
                logger.debug(f"Found {data_type} equipment with serial {serial}")
                return converted_equipment
            else:
                logger.debug(f"No {data_type} equipment found with serial {serial}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get {data_type} equipment by serial {serial}: {e}")
            return None

    def migrate_json_to_supabase(self, data_type: Literal['ppm', 'ocm'], json_data: List[Dict[str, Any]]) -> bool:
        """
        Migrate equipment data from JSON format to Supabase.
        
        Args:
            data_type: Type of equipment data ('ppm' or 'ocm')
            json_data: Equipment data from JSON files
            
        Returns:
            True if migration successful, False otherwise
        """
        try:
            if not json_data:
                logger.info(f"No {data_type} data to migrate")
                return True
            
            table_name = self.get_table_name(data_type)
            
            # Convert JSON data to Supabase format
            supabase_records = []
            for equipment in json_data:
                supabase_record = self._convert_app_to_supabase_format(equipment, data_type)
                supabase_records.append(supabase_record)
            
            # Insert data into Supabase
            result = self.supabase.table(table_name).insert(supabase_records).execute()
            
            logger.info(f"Successfully migrated {len(supabase_records)} {data_type} equipment records to Supabase")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate {data_type} equipment data to Supabase: {e}")
            return False

    def _convert_app_to_supabase_format(self, equipment: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        Convert application equipment data to Supabase format.
        
        Args:
            equipment: Equipment data from application
            data_type: Type of equipment ('ppm' or 'ocm')
            
        Returns:
            Equipment data in Supabase format
        """
        try:
            # Base conversion
            converted = {}
            
            # Map fields from application format to Supabase format
            if data_type == 'ppm':
                field_mapping = {
                    'SERIAL': 'serial_number',
                    'EQUIPMENT': 'equipment_name',
                    'MODEL': 'model',
                    'DEPARTMENT': 'department',
                    'LOCATION': 'location',
                    'INSTALLATION_DATE': 'installation_date',
                    'WARRANTY_END': 'warranty_end',
                    'Status': 'status',
                    'NOTES': 'notes'
                }
            else:  # OCM
                field_mapping = {
                    'Serial': 'serial_number',
                    'EQUIPMENT': 'equipment_name',
                    'MODEL': 'model',
                    'DEPARTMENT': 'department',
                    'LOCATION': 'location',
                    'INSTALLATION_DATE': 'installation_date',
                    'WARRANTY_END': 'warranty_end',
                    'Status': 'status',
                    'NOTES': 'notes'
                }
            
            # Apply field mapping
            for app_field, supabase_field in field_mapping.items():
                if app_field in equipment:
                    converted[supabase_field] = equipment[app_field]
            
            # Ensure required fields
            if 'serial_number' not in converted:
                converted['serial_number'] = equipment.get('id', f'UNKNOWN_{data_type.upper()}')
            
            if 'equipment_name' not in converted:
                converted['equipment_name'] = 'Unknown Equipment'
            
            if 'status' not in converted:
                converted['status'] = 'Active'
            
            return converted
            
        except Exception as e:
            logger.error(f"Error converting equipment data to Supabase format: {e}")
            return equipment


# Global instance for backward compatibility
_equipment_service = None

def get_equipment_service() -> SupabaseEquipmentService:
    """Get the global equipment service instance."""
    global _equipment_service
    if _equipment_service is None:
        _equipment_service = SupabaseEquipmentService()
    return _equipment_service
