#!/usr/bin/env python3
"""
Debug equipment data loading issues
"""

import os
import sys
import logging
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config
from app.services.data_service import DataService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_json_files():
    """Check equipment data in JSON files."""
    logger.info("=== Checking JSON Files ===")
    
    json_files = {
        'PPM': 'data/ppm.json',
        'OCM': 'data/ocm.json',
        'Equipment History': 'data/equipment_history.json'
    }
    
    for name, file_path in json_files.items():
        try:
            if Path(file_path).exists():
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    logger.info(f"{name}: {len(data)} records in {file_path}")
                    if len(data) > 0:
                        logger.info(f"  Sample record: {data[0] if isinstance(data, list) else 'Not a list'}")
            else:
                logger.warning(f"{name}: File {file_path} does not exist")
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")

def check_supabase_tables():
    """Check if equipment tables exist in Supabase."""
    logger.info("=== Checking Supabase Tables ===")
    
    try:
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            logger.error("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
            return False
        
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        
        # Check for equipment-related tables
        tables_to_check = ['ppm_equipment', 'ocm_equipment', 'equipment', 'equipment_history']
        
        for table_name in tables_to_check:
            try:
                result = supabase.table(table_name).select('*').limit(1).execute()
                logger.info(f"✓ Table '{table_name}' exists and is accessible")
                
                # Get count
                count_result = supabase.table(table_name).select('*', count='exact').execute()
                logger.info(f"  - {count_result.count} records in {table_name}")
                
            except Exception as e:
                logger.warning(f"✗ Table '{table_name}' issue: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error connecting to Supabase: {e}")
        return False

def check_data_service():
    """Check DataService equipment loading methods."""
    logger.info("=== Checking DataService Methods ===")
    
    try:
        # Test PPM data loading
        logger.info("Testing PPM data loading...")
        ppm_data = DataService.load_ppm_data()
        logger.info(f"PPM data loaded: {len(ppm_data)} records")
        
        # Test OCM data loading
        logger.info("Testing OCM data loading...")
        ocm_data = DataService.load_ocm_data()
        logger.info(f"OCM data loaded: {len(ocm_data)} records")
        
        # Test equipment history loading
        logger.info("Testing equipment history loading...")
        history_data = DataService.load_equipment_history()
        logger.info(f"Equipment history loaded: {len(history_data)} records")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing DataService: {e}")
        return False

def check_equipment_routes():
    """Check if equipment routes are working."""
    logger.info("=== Checking Equipment Routes ===")
    
    try:
        import requests
        
        # Test PPM API endpoint
        try:
            response = requests.get('http://localhost:5001/api/equipment/ppm', timeout=10)
            logger.info(f"PPM API endpoint: Status {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"PPM API returned: {len(data)} records")
        except Exception as e:
            logger.warning(f"PPM API endpoint error: {e}")
        
        # Test OCM API endpoint
        try:
            response = requests.get('http://localhost:5001/api/equipment/ocm', timeout=10)
            logger.info(f"OCM API endpoint: Status {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"OCM API returned: {len(data)} records")
        except Exception as e:
            logger.warning(f"OCM API endpoint error: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing equipment routes: {e}")
        return False

def create_sample_data():
    """Create sample equipment data for testing."""
    logger.info("=== Creating Sample Equipment Data ===")
    
    try:
        # Sample PPM data
        sample_ppm = [
            {
                "id": 1,
                "equipment_name": "X-Ray Machine",
                "model": "XR-2000",
                "serial_number": "XR001",
                "department": "Radiology",
                "location": "Room 101",
                "installation_date": "2020-01-15",
                "warranty_end": "2025-01-15",
                "q1_date": "2024-03-15",
                "q2_date": "2024-06-15",
                "q3_date": "2024-09-15",
                "q4_date": "2024-12-15",
                "status": "Active",
                "notes": "Regular maintenance required"
            },
            {
                "id": 2,
                "equipment_name": "MRI Scanner",
                "model": "MRI-3000",
                "serial_number": "MRI001",
                "department": "Radiology",
                "location": "Room 102",
                "installation_date": "2021-05-20",
                "warranty_end": "2026-05-20",
                "q1_date": "2024-03-20",
                "q2_date": "2024-06-20",
                "q3_date": "2024-09-20",
                "q4_date": "2024-12-20",
                "status": "Active",
                "notes": "High priority equipment"
            }
        ]
        
        # Sample OCM data
        sample_ocm = [
            {
                "id": 1,
                "equipment_name": "Ventilator",
                "model": "VENT-500",
                "serial_number": "VENT001",
                "department": "ICU",
                "location": "ICU-A",
                "installation_date": "2022-03-10",
                "warranty_end": "2027-03-10",
                "status": "Active",
                "notes": "Critical care equipment"
            },
            {
                "id": 2,
                "equipment_name": "Defibrillator",
                "model": "DEFIB-200",
                "serial_number": "DEFIB001",
                "department": "Emergency",
                "location": "ER-1",
                "installation_date": "2021-08-15",
                "warranty_end": "2026-08-15",
                "status": "Active",
                "notes": "Emergency equipment"
            }
        ]
        
        # Save to JSON files
        with open('data/ppm.json', 'w') as f:
            json.dump(sample_ppm, f, indent=2)
        logger.info(f"Created sample PPM data: {len(sample_ppm)} records")
        
        with open('data/ocm.json', 'w') as f:
            json.dump(sample_ocm, f, indent=2)
        logger.info(f"Created sample OCM data: {len(sample_ocm)} records")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        return False

def main():
    """Main debugging function."""
    logger.info("=== ALORF BIOMED Equipment Data Debug Started ===")
    
    # Step 1: Check JSON files
    check_json_files()
    
    # Step 2: Check Supabase tables
    supabase_ok = check_supabase_tables()
    
    # Step 3: Check DataService
    data_service_ok = check_data_service()
    
    # Step 4: Check equipment routes
    routes_ok = check_equipment_routes()
    
    # Step 5: Create sample data if needed
    logger.info("\n=== Analysis ===")
    if not data_service_ok:
        logger.info("DataService is not loading equipment data correctly")
        logger.info("Creating sample data for testing...")
        create_sample_data()
        
        # Re-test after creating sample data
        logger.info("Re-testing DataService after creating sample data...")
        data_service_ok = check_data_service()
    
    # Summary
    logger.info("\n=== Summary ===")
    logger.info(f"Supabase connection: {'✓' if supabase_ok else '✗'}")
    logger.info(f"DataService working: {'✓' if data_service_ok else '✗'}")
    logger.info(f"Equipment routes: {'✓' if routes_ok else '✗'}")
    
    if data_service_ok:
        logger.info("✅ Equipment data should now be available!")
    else:
        logger.error("❌ Equipment data issues remain")
    
    return data_service_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
