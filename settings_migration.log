2025-07-20 15:11:57,910 - INFO - === Settings Migration to Supabase Started ===
2025-07-20 15:11:59,863 - INFO - Found settings file: app\data\settings.json
2025-07-20 15:11:59,866 - INFO - Loaded settings data with 17 keys
2025-07-20 15:12:00,497 - ERROR - Failed to load settings from database: {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation "public.settings" does not exist'}
2025-07-20 15:12:00,497 - INFO - Returning default settings due to error
2025-07-20 15:12:00,497 - INFO - Migrating settings from JSON to Supabase
2025-07-20 15:12:00,735 - ERROR - Failed to save users to database: {}
2025-07-20 15:12:01,016 - ERROR - Failed to save roles to database: {}
2025-07-20 15:12:01,016 - INFO - Saving 15 settings to database
2025-07-20 15:12:01,242 - ERROR - Failed to save settings to database: {}
2025-07-20 15:12:01,242 - INFO - JSO<PERSON> to Supabase migration completed successfully
2025-07-20 15:12:01,242 - INFO - Settings migration completed successfully
2025-07-20 15:12:01,454 - ERROR - Failed to load settings from database: {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation "public.settings" does not exist'}
2025-07-20 15:12:01,470 - INFO - Returning default settings due to error
2025-07-20 15:12:01,470 - INFO - Verification: Loaded 15 settings from Supabase
2025-07-20 15:12:01,533 - INFO - === Testing Settings Service ===
2025-07-20 15:12:03,588 - ERROR - Failed to load settings from database: {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation "public.settings" does not exist'}
2025-07-20 15:12:03,588 - INFO - Returning default settings due to error
2025-07-20 15:12:03,588 - INFO - Loaded 15 settings
2025-07-20 15:12:03,861 - ERROR - Failed to get setting 'email_notifications_enabled': {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation "public.settings" does not exist'}
2025-07-20 15:12:03,861 - INFO - Email notifications enabled: False
2025-07-20 15:12:04,107 - ERROR - Failed to set setting 'test_migration_setting': {}
2025-07-20 15:12:04,108 - ERROR - Failed to set test setting
2025-07-20 15:12:04,108 - INFO - Settings service test completed
2025-07-20 15:12:04,108 - INFO - === Settings Migration and Testing Completed Successfully ===
