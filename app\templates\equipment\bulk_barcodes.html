{% extends 'base.html' %}

{% block title %}Bulk Barcodes - {{ data_type.upper() }}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Bulk Barcodes - {{ data_type.upper() }} Equipment</h4>
                    <div>
                        <a href="{{ url_for('views.download_bulk_barcodes', data_type=data_type) }}" 
                           class="btn btn-primary me-2">
                            <i class="fas fa-download"></i> Download All as ZIP
                        </a>
                        <button onclick="window.print()" class="btn btn-secondary me-2">
                            <i class="fas fa-print"></i> Print All
                        </button>
                        <a href="{{ url_for('views.list_equipment', data_type=data_type) }}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if barcodes %}
                        <div class="row">
                            {% for barcode_data in barcodes %}
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card barcode-card">
                                        <div class="card-body text-center">
                                            <h6>{{ barcode_data.serial }}</h6>
                                            {% if barcode_data.equipment.get('Name') or barcode_data.equipment.get('MODEL') or barcode_data.equipment.get('Model') %}
                                                <p class="text-muted small">{{ barcode_data.equipment.get('Name') or barcode_data.equipment.get('MODEL') or barcode_data.equipment.get('Model') }}</p>
                                            {% endif %}
                                            {% if barcode_data.equipment.get('Department') %}
                                                <p class="text-muted small">{{ barcode_data.equipment.get('Department') }}</p>
                                            {% endif %}
                                            
                                            <div class="barcode-container">
                                                <img src="data:image/png;base64,{{ barcode_data.barcode_base64 }}" 
                                                     alt="Barcode for {{ barcode_data.serial }}" 
                                                     class="img-fluid">
                                            </div>
                                            
                                            <div class="mt-2">
                                                <a href="{{ url_for('views.download_barcode', data_type=data_type, serial=barcode_data.serial) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center">
                            <p>No equipment found to generate barcodes.</p>
                            <a href="{{ url_for('views.list_equipment', data_type=data_type) }}" 
                               class="btn btn-primary">
                                Back to Equipment List
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.barcode-card {
    height: 100%;
    page-break-inside: avoid;
}

@media print {
    .btn, .card-header .btn, .navbar, .footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        margin-bottom: 10px !important;
    }
    
    .barcode-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    .col-md-6, .col-lg-4 {
        width: 33.333% !important;
        float: left;
    }
}
</style>
{% endblock %}

