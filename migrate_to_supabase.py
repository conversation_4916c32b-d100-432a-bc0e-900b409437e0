#!/usr/bin/env python3
"""
Main data migration script for ALORF BIOMED System.
Migrates all JSON data to Supabase PostgreSQL database.
"""
import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataMigrator:
    """Main class for migrating JSON data to Supabase."""
    
    def __init__(self):
        self.engine = None
        self.migration_stats = {
            'departments': {'total': 0, 'migrated': 0, 'errors': 0},
            'trainers': {'total': 0, 'migrated': 0, 'errors': 0},
            'ppm_equipment': {'total': 0, 'migrated': 0, 'errors': 0},
            'ppm_quarters': {'total': 0, 'migrated': 0, 'errors': 0},
            'ocm_equipment': {'total': 0, 'migrated': 0, 'errors': 0},
            'training_records': {'total': 0, 'migrated': 0, 'errors': 0},
            'machine_trainer_assignments': {'total': 0, 'migrated': 0, 'errors': 0},
            'application_settings': {'total': 0, 'migrated': 0, 'errors': 0},
            'audit_logs': {'total': 0, 'migrated': 0, 'errors': 0},
            'equipment_history': {'total': 0, 'migrated': 0, 'errors': 0}
        }
        self._init_database()
    
    def _init_database(self):
        """Initialize database connection."""
        try:
            from sqlalchemy import create_engine
            
            database_url = os.getenv('DATABASE_URL')
            if not database_url:
                raise ValueError("DATABASE_URL not set in environment")
            
            self.engine = create_engine(database_url, pool_pre_ping=True)
            logger.info("✅ Database connection initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
            raise
    
    def load_json_file(self, file_path: str) -> Optional[List[Dict[str, Any]]]:
        """Load data from JSON file."""
        try:
            if not os.path.exists(file_path):
                logger.warning(f"⚠️  File not found: {file_path}")
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                logger.info(f"📁 Loaded {len(data)} records from {file_path}")
                return data
            elif isinstance(data, dict):
                logger.info(f"📁 Loaded settings data from {file_path}")
                return [data]  # Wrap single dict in list for consistency
            else:
                logger.error(f"❌ Unexpected data format in {file_path}")
                return []
                
        except Exception as e:
            logger.error(f"❌ Failed to load {file_path}: {e}")
            return None
    
    def migrate_departments(self) -> bool:
        """Migrate departments data."""
        logger.info("🏢 Migrating departments...")
        
        data = self.load_json_file('data/departments.json')
        if data is None:
            return False
        
        self.migration_stats['departments']['total'] = len(data)
        
        try:
            from sqlalchemy import text
            
            with self.engine.connect() as conn:
                for dept in data:
                    try:
                        sql = text("""
                            INSERT INTO departments (id, department_name, information, created_date, updated_date)
                            VALUES (:id, :department_name, :information, :created_date, :updated_date)
                            ON CONFLICT (id) DO UPDATE SET
                                department_name = EXCLUDED.department_name,
                                information = EXCLUDED.information,
                                updated_date = EXCLUDED.updated_date
                        """)
                        
                        conn.execute(sql, {
                            'id': dept.get('id'),
                            'department_name': dept.get('department_name'),
                            'information': dept.get('information'),
                            'created_date': dept.get('created_date'),
                            'updated_date': dept.get('updated_date')
                        })
                        
                        self.migration_stats['departments']['migrated'] += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to migrate department {dept.get('id')}: {e}")
                        self.migration_stats['departments']['errors'] += 1
                
                conn.commit()
                
                # Update sequence to prevent ID conflicts
                conn.execute(text("SELECT setval('departments_id_seq', (SELECT MAX(id) FROM departments))"))
                conn.commit()
            
            logger.info(f"✅ Departments migration completed: {self.migration_stats['departments']['migrated']}/{self.migration_stats['departments']['total']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Departments migration failed: {e}")
            return False
    
    def migrate_trainers(self) -> bool:
        """Migrate trainers data."""
        logger.info("👨‍🏫 Migrating trainers...")
        
        data = self.load_json_file('data/trainers.json')
        if data is None:
            return False
        
        self.migration_stats['trainers']['total'] = len(data)
        
        try:
            from sqlalchemy import text
            
            with self.engine.connect() as conn:
                for trainer in data:
                    try:
                        sql = text("""
                            INSERT INTO trainers (id, name, department_id, telephone, information, created_date, updated_date)
                            VALUES (:id, :name, :department_id, :telephone, :information, :created_date, :updated_date)
                            ON CONFLICT (id) DO UPDATE SET
                                name = EXCLUDED.name,
                                department_id = EXCLUDED.department_id,
                                telephone = EXCLUDED.telephone,
                                information = EXCLUDED.information,
                                updated_date = EXCLUDED.updated_date
                        """)
                        
                        conn.execute(sql, {
                            'id': trainer.get('id'),
                            'name': trainer.get('name'),
                            'department_id': trainer.get('department_id'),
                            'telephone': trainer.get('telephone'),
                            'information': trainer.get('information'),
                            'created_date': trainer.get('created_date'),
                            'updated_date': trainer.get('updated_date')
                        })
                        
                        self.migration_stats['trainers']['migrated'] += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to migrate trainer {trainer.get('id')}: {e}")
                        self.migration_stats['trainers']['errors'] += 1
                
                conn.commit()
                
                # Update sequence
                conn.execute(text("SELECT setval('trainers_id_seq', (SELECT MAX(id) FROM trainers))"))
                conn.commit()
            
            logger.info(f"✅ Trainers migration completed: {self.migration_stats['trainers']['migrated']}/{self.migration_stats['trainers']['total']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Trainers migration failed: {e}")
            return False
    
    def migrate_ppm_equipment(self) -> bool:
        """Migrate PPM equipment data."""
        logger.info("🔧 Migrating PPM equipment...")
        
        data = self.load_json_file('data/ppm.json')
        if data is None:
            return False
        
        self.migration_stats['ppm_equipment']['total'] = len(data)
        
        try:
            from sqlalchemy import text
            
            with self.engine.connect() as conn:
                for ppm in data:
                    try:
                        # Insert PPM equipment record
                        sql = text("""
                            INSERT INTO ppm_equipment (no, department, name, model, serial, manufacturer, 
                                                     log_number, installation_date, warranty_end, status, has_history)
                            VALUES (:no, :department, :name, :model, :serial, :manufacturer, 
                                   :log_number, :installation_date, :warranty_end, :status, :has_history)
                            RETURNING id
                        """)
                        
                        result = conn.execute(sql, {
                            'no': ppm.get('NO'),
                            'department': ppm.get('Department'),
                            'name': ppm.get('Name'),
                            'model': ppm.get('MODEL'),
                            'serial': ppm.get('SERIAL'),
                            'manufacturer': ppm.get('MANUFACTURER'),
                            'log_number': ppm.get('LOG_Number'),
                            'installation_date': ppm.get('Installation_Date'),
                            'warranty_end': ppm.get('Warranty_End'),
                            'status': ppm.get('Status', 'Upcoming'),
                            'has_history': ppm.get('has_history', False)
                        })
                        
                        equipment_id = result.fetchone()[0]
                        
                        # Insert quarterly data
                        quarters = ['PPM_Q_I', 'PPM_Q_II', 'PPM_Q_III', 'PPM_Q_IV']
                        quarter_names = ['Q_I', 'Q_II', 'Q_III', 'Q_IV']
                        
                        for quarter_key, quarter_name in zip(quarters, quarter_names):
                            quarter_data = ppm.get(quarter_key, {})
                            if quarter_data:
                                quarter_sql = text("""
                                    INSERT INTO ppm_quarters (ppm_equipment_id, quarter, engineer, quarter_date, status)
                                    VALUES (:equipment_id, :quarter, :engineer, :quarter_date, :status)
                                """)
                                
                                conn.execute(quarter_sql, {
                                    'equipment_id': equipment_id,
                                    'quarter': quarter_name,
                                    'engineer': quarter_data.get('engineer'),
                                    'quarter_date': quarter_data.get('quarter_date'),
                                    'status': quarter_data.get('status')
                                })
                                
                                self.migration_stats['ppm_quarters']['migrated'] += 1
                        
                        self.migration_stats['ppm_equipment']['migrated'] += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to migrate PPM equipment {ppm.get('SERIAL')}: {e}")
                        self.migration_stats['ppm_equipment']['errors'] += 1
                
                conn.commit()
            
            logger.info(f"✅ PPM equipment migration completed: {self.migration_stats['ppm_equipment']['migrated']}/{self.migration_stats['ppm_equipment']['total']}")
            logger.info(f"✅ PPM quarters migration completed: {self.migration_stats['ppm_quarters']['migrated']} quarters")
            return True
            
        except Exception as e:
            logger.error(f"❌ PPM equipment migration failed: {e}")
            return False

    def migrate_ocm_equipment(self) -> bool:
        """Migrate OCM equipment data."""
        logger.info("🔧 Migrating OCM equipment...")

        data = self.load_json_file('data/ocm.json')
        if data is None:
            return False

        self.migration_stats['ocm_equipment']['total'] = len(data)

        try:
            from sqlalchemy import text

            with self.engine.connect() as conn:
                for ocm in data:
                    try:
                        sql = text("""
                            INSERT INTO ocm_equipment (no, department, name, model, serial, manufacturer,
                                                     log_number, installation_date, warranty_end, service_date,
                                                     engineer, next_maintenance, status, has_history)
                            VALUES (:no, :department, :name, :model, :serial, :manufacturer,
                                   :log_number, :installation_date, :warranty_end, :service_date,
                                   :engineer, :next_maintenance, :status, :has_history)
                        """)

                        conn.execute(sql, {
                            'no': ocm.get('NO'),
                            'department': ocm.get('Department'),
                            'name': ocm.get('Name'),
                            'model': ocm.get('Model'),
                            'serial': ocm.get('Serial'),
                            'manufacturer': ocm.get('Manufacturer'),
                            'log_number': ocm.get('Log_Number'),
                            'installation_date': ocm.get('Installation_Date'),
                            'warranty_end': ocm.get('Warranty_End'),
                            'service_date': ocm.get('Service_Date'),
                            'engineer': ocm.get('Engineer'),
                            'next_maintenance': ocm.get('Next_Maintenance'),
                            'status': ocm.get('Status', 'Upcoming'),
                            'has_history': ocm.get('has_history', False)
                        })

                        self.migration_stats['ocm_equipment']['migrated'] += 1

                    except Exception as e:
                        logger.error(f"❌ Failed to migrate OCM equipment {ocm.get('Serial')}: {e}")
                        self.migration_stats['ocm_equipment']['errors'] += 1

                conn.commit()

            logger.info(f"✅ OCM equipment migration completed: {self.migration_stats['ocm_equipment']['migrated']}/{self.migration_stats['ocm_equipment']['total']}")
            return True

        except Exception as e:
            logger.error(f"❌ OCM equipment migration failed: {e}")
            return False

    def migrate_training_records(self) -> bool:
        """Migrate training records data."""
        logger.info("📚 Migrating training records...")

        data = self.load_json_file('data/training.json')
        if data is None:
            return False

        self.migration_stats['training_records']['total'] = len(data)

        try:
            from sqlalchemy import text

            with self.engine.connect() as conn:
                for training in data:
                    try:
                        # Insert training record
                        sql = text("""
                            INSERT INTO training_records (employee_id, name, department, last_trained_date, next_due_date)
                            VALUES (:employee_id, :name, :department, :last_trained_date, :next_due_date)
                            RETURNING id
                        """)

                        result = conn.execute(sql, {
                            'employee_id': training.get('employee_id'),
                            'name': training.get('name'),
                            'department': training.get('department'),
                            'last_trained_date': training.get('last_trained_date'),
                            'next_due_date': training.get('next_due_date')
                        })

                        training_id = result.fetchone()[0]

                        # Insert machine trainer assignments
                        assignments = training.get('machine_trainer_assignments', [])
                        for assignment in assignments:
                            if assignment and isinstance(assignment, dict):
                                assignment_sql = text("""
                                    INSERT INTO machine_trainer_assignments (training_record_id, machine, trainer)
                                    VALUES (:training_id, :machine, :trainer)
                                """)

                                conn.execute(assignment_sql, {
                                    'training_id': training_id,
                                    'machine': assignment.get('machine'),
                                    'trainer': assignment.get('trainer')
                                })

                                self.migration_stats['machine_trainer_assignments']['migrated'] += 1

                        self.migration_stats['training_records']['migrated'] += 1

                    except Exception as e:
                        logger.error(f"❌ Failed to migrate training record {training.get('id')}: {e}")
                        self.migration_stats['training_records']['errors'] += 1

                conn.commit()

            logger.info(f"✅ Training records migration completed: {self.migration_stats['training_records']['migrated']}/{self.migration_stats['training_records']['total']}")
            logger.info(f"✅ Machine trainer assignments migration completed: {self.migration_stats['machine_trainer_assignments']['migrated']} assignments")
            return True

        except Exception as e:
            logger.error(f"❌ Training records migration failed: {e}")
            return False

    def migrate_application_settings(self) -> bool:
        """Migrate application settings data."""
        logger.info("⚙️ Migrating application settings...")

        data = self.load_json_file('data/settings.json')
        if data is None:
            return False

        # Settings file is a single dict, not a list
        if isinstance(data, list) and len(data) > 0:
            settings_data = data[0]
        else:
            settings_data = data if isinstance(data, dict) else {}

        self.migration_stats['application_settings']['total'] = len(settings_data)

        try:
            from sqlalchemy import text

            with self.engine.connect() as conn:
                for key, value in settings_data.items():
                    try:
                        sql = text("""
                            INSERT INTO application_settings (setting_key, setting_value)
                            VALUES (:key, :value)
                            ON CONFLICT (setting_key) DO UPDATE SET
                                setting_value = EXCLUDED.setting_value,
                                updated_at = NOW()
                        """)

                        # Convert value to JSON
                        import json
                        json_value = json.dumps(value) if not isinstance(value, str) else json.dumps(value)

                        conn.execute(sql, {
                            'key': key,
                            'value': json_value
                        })

                        self.migration_stats['application_settings']['migrated'] += 1

                    except Exception as e:
                        logger.error(f"❌ Failed to migrate setting {key}: {e}")
                        self.migration_stats['application_settings']['errors'] += 1

                conn.commit()

            logger.info(f"✅ Application settings migration completed: {self.migration_stats['application_settings']['migrated']}/{self.migration_stats['application_settings']['total']}")
            return True

        except Exception as e:
            logger.error(f"❌ Application settings migration failed: {e}")
            return False

    def migrate_audit_logs(self) -> bool:
        """Migrate audit logs data."""
        logger.info("📋 Migrating audit logs...")

        data = self.load_json_file('data/audit_log.json')
        if data is None:
            return False

        self.migration_stats['audit_logs']['total'] = len(data)

        try:
            from sqlalchemy import text
            import json

            with self.engine.connect() as conn:
                for log in data:
                    try:
                        sql = text("""
                            INSERT INTO audit_logs (timestamp, user_id, action, table_name, record_id,
                                                  old_values, new_values, ip_address)
                            VALUES (:timestamp, :user_id, :action, :table_name, :record_id,
                                   :old_values, :new_values, :ip_address)
                        """)

                        conn.execute(sql, {
                            'timestamp': log.get('timestamp'),
                            'user_id': log.get('user_id'),
                            'action': log.get('action'),
                            'table_name': log.get('table_name'),
                            'record_id': log.get('record_id'),
                            'old_values': json.dumps(log.get('old_values')) if log.get('old_values') else None,
                            'new_values': json.dumps(log.get('new_values')) if log.get('new_values') else None,
                            'ip_address': log.get('ip_address')
                        })

                        self.migration_stats['audit_logs']['migrated'] += 1

                    except Exception as e:
                        logger.error(f"❌ Failed to migrate audit log: {e}")
                        self.migration_stats['audit_logs']['errors'] += 1

                conn.commit()

            logger.info(f"✅ Audit logs migration completed: {self.migration_stats['audit_logs']['migrated']}/{self.migration_stats['audit_logs']['total']}")
            return True

        except Exception as e:
            logger.error(f"❌ Audit logs migration failed: {e}")
            return False

    def migrate_equipment_history(self) -> bool:
        """Migrate equipment history data."""
        logger.info("📝 Migrating equipment history...")

        data = self.load_json_file('data/equipment_history.json')
        if data is None:
            return False

        self.migration_stats['equipment_history']['total'] = len(data)

        try:
            from sqlalchemy import text

            with self.engine.connect() as conn:
                for history in data:
                    try:
                        sql = text("""
                            INSERT INTO equipment_history (equipment_type, equipment_id, serial_number,
                                                         note, attachment_filename, attachment_data, created_by)
                            VALUES (:equipment_type, :equipment_id, :serial_number,
                                   :note, :attachment_filename, :attachment_data, :created_by)
                        """)

                        # Handle attachment data (convert base64 to bytes if needed)
                        attachment_data = None
                        if history.get('attachment_data'):
                            import base64
                            try:
                                attachment_data = base64.b64decode(history['attachment_data'])
                            except:
                                attachment_data = history['attachment_data'].encode('utf-8')

                        conn.execute(sql, {
                            'equipment_type': history.get('equipment_type'),
                            'equipment_id': history.get('equipment_id'),
                            'serial_number': history.get('serial_number'),
                            'note': history.get('note'),
                            'attachment_filename': history.get('attachment_filename'),
                            'attachment_data': attachment_data,
                            'created_by': history.get('created_by')
                        })

                        self.migration_stats['equipment_history']['migrated'] += 1

                    except Exception as e:
                        logger.error(f"❌ Failed to migrate equipment history: {e}")
                        self.migration_stats['equipment_history']['errors'] += 1

                conn.commit()

            logger.info(f"✅ Equipment history migration completed: {self.migration_stats['equipment_history']['migrated']}/{self.migration_stats['equipment_history']['total']}")
            return True

        except Exception as e:
            logger.error(f"❌ Equipment history migration failed: {e}")
            return False

    def run_migration(self) -> bool:
        """Run the complete migration process."""
        logger.info("🚀 Starting complete data migration to Supabase...")
        logger.info("=" * 60)

        # Migration order (respecting dependencies)
        migrations = [
            ("Departments", self.migrate_departments),
            ("Trainers", self.migrate_trainers),
            ("PPM Equipment", self.migrate_ppm_equipment),
            ("OCM Equipment", self.migrate_ocm_equipment),
            ("Training Records", self.migrate_training_records),
            ("Application Settings", self.migrate_application_settings),
            ("Audit Logs", self.migrate_audit_logs),
            ("Equipment History", self.migrate_equipment_history)
        ]

        successful_migrations = 0
        total_migrations = len(migrations)

        for migration_name, migration_func in migrations:
            logger.info(f"\n📋 Starting {migration_name} migration...")
            try:
                if migration_func():
                    successful_migrations += 1
                    logger.info(f"✅ {migration_name} migration completed successfully")
                else:
                    logger.error(f"❌ {migration_name} migration failed")
            except Exception as e:
                logger.error(f"❌ {migration_name} migration crashed: {e}")

        # Print summary
        self.print_migration_summary()

        success_rate = successful_migrations / total_migrations
        if success_rate == 1.0:
            logger.info("🎉 All migrations completed successfully!")
            return True
        elif success_rate >= 0.8:
            logger.warning(f"⚠️  Most migrations completed ({successful_migrations}/{total_migrations})")
            return True
        else:
            logger.error(f"❌ Migration failed ({successful_migrations}/{total_migrations} successful)")
            return False

    def print_migration_summary(self):
        """Print detailed migration summary."""
        logger.info("\n" + "=" * 60)
        logger.info("📊 MIGRATION SUMMARY")
        logger.info("=" * 60)

        total_records = 0
        total_migrated = 0
        total_errors = 0

        for table_name, stats in self.migration_stats.items():
            if stats['total'] > 0:
                success_rate = (stats['migrated'] / stats['total']) * 100 if stats['total'] > 0 else 0
                status = "✅" if stats['errors'] == 0 else "⚠️" if success_rate >= 80 else "❌"

                logger.info(f"{table_name:.<25} {status} {stats['migrated']:>4}/{stats['total']:<4} ({success_rate:5.1f}%)")

                total_records += stats['total']
                total_migrated += stats['migrated']
                total_errors += stats['errors']

        logger.info("=" * 60)
        overall_success_rate = (total_migrated / total_records) * 100 if total_records > 0 else 0
        logger.info(f"{'TOTAL':.<25} {'✅' if total_errors == 0 else '⚠️'} {total_migrated:>4}/{total_records:<4} ({overall_success_rate:5.1f}%)")

        if total_errors > 0:
            logger.warning(f"⚠️  {total_errors} errors encountered during migration")

        logger.info("=" * 60)

    def verify_migration(self) -> bool:
        """Verify migration by checking record counts."""
        logger.info("🔍 Verifying migration results...")

        try:
            from sqlalchemy import text

            with self.engine.connect() as conn:
                # Check each table
                tables_to_check = [
                    'departments', 'trainers', 'ppm_equipment', 'ppm_quarters',
                    'ocm_equipment', 'training_records', 'machine_trainer_assignments',
                    'application_settings', 'audit_logs', 'equipment_history'
                ]

                for table in tables_to_check:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.fetchone()[0]
                    logger.info(f"✅ {table}: {count} records")

                return True

        except Exception as e:
            logger.error(f"❌ Migration verification failed: {e}")
            return False

def main():
    """Main function."""
    logger.info("🚀 ALORF BIOMED Data Migration to Supabase")
    logger.info("=" * 60)

    # Check environment
    if not os.getenv('DATABASE_URL'):
        logger.error("❌ DATABASE_URL not set. Please configure Supabase connection.")
        return False

    # Create migrator and run migration
    try:
        migrator = DataMigrator()

        # Run migration
        if migrator.run_migration():
            # Verify results
            if migrator.verify_migration():
                logger.info("🎉 Migration completed and verified successfully!")
                logger.info("✅ You can now switch to Supabase by setting USE_SUPABASE=true")
                return True
            else:
                logger.error("❌ Migration verification failed")
                return False
        else:
            logger.error("❌ Migration failed")
            return False

    except Exception as e:
        logger.error(f"❌ Migration process failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
