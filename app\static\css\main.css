/* Import Google Font - Roboto */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap');

/* General Styles - Full Screen Optimized */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa; /* Light gray background */
    color: #343a40; /* Dark gray text */
    margin: 0;
    padding: 0;
    width: 100vw;
    min-height: 100vh;
    overflow-x: hidden;
}

html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}

.container {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
}

/* Full-screen container class */
.container-full {
    width: 100%;
    margin: 0;
    padding: 20px;
}

/* Full width wrapper for maximum screen utilization */
.full-width-wrapper {
    width: 100vw;
    margin: 0;
    padding: 0;
}

/* Enhanced heading styles */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

/* Page title specific styling */
.page-title, h1 {
    color: #2c3e50;
    font-weight: 600;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
    margin-bottom: 30px;
    margin-top: 20px;
    padding-top: 15px;
}

a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #764ba2;
    text-decoration: none;
}

/* Modern Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-secondary {
    background: #6c757d;
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    background: #5a6268;
}

/* Tables - Enhanced for Full Screen */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    background-color: #fff;
    border-collapse: collapse;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 12px;
    overflow: hidden;
    font-size: 0.9rem;
}

.table th, .table td {
    padding: 12px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
}

.table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
    border: none;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.3s ease;
}

/* Enhanced table responsiveness */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Forms - Optimized for full screen */
form {
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    width: 100%;
    border: none;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

input[type="text"],
input[type="date"],
input[type="email"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 12px 16px;
    margin-bottom: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    box-sizing: border-box;
    font-size: 0.95rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="text"]:focus,
input[type="date"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Cards - Full screen optimized */
.card {
    height: 100%;
    border: none;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.5rem;
    flex: 1 1 auto;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border: none;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Dashboard specific optimizations */
.row {
    margin-left: 0;
    margin-right: 0;
}

.col, .col-md-3, .col-md-auto, .col-12 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Status badges */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.8rem;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
    .table {
        font-size: 0.8rem;
    }
    
    .table th, .table td {
        padding: 8px;
    }
    
    .container-fluid-custom {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    form {
        padding: 20px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Utility classes for full-screen layouts */
.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

.vh-100 {
    height: 100vh !important;
}

.vw-100 {
    width: 100vw !important;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #764ba2;
}

/* Machine/Trainer styling for Training Management */
.table td .machine-number {
    font-weight: 600 !important;
    color: #6c757d !important;
}

.table td .machine-name {
    font-weight: 500 !important;
    color: #212529 !important;
}

.table td .trainer-name {
    font-weight: bold !important;
    color: #dc3545 !important;
}

/* Additional specificity for trainer names */
.table tbody tr td .trainer-name,
.table tbody tr td span.trainer-name {
    font-weight: bold !important;
    color: #dc3545 !important;
}
