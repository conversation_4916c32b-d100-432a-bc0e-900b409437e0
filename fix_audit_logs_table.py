#!/usr/bin/env python3
"""
Check and fix the audit_logs table schema
"""

import os
import sys
import logging

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from supabase import create_client, Client
from app.config import Config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_audit_logs_table():
    """Check the current audit_logs table structure."""
    try:
        if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set")
        
        supabase: Client = create_client(Config.SUPABASE_URL, Config.SUPABASE_SERVICE_KEY)
        logger.info("Supabase client initialized")
        
        # Try to get the table structure by attempting different column combinations
        test_columns = [
            ['id'],
            ['id', 'timestamp'],
            ['id', 'timestamp', 'event_type'],
            ['id', 'timestamp', 'event_type', 'performed_by'],
            ['id', 'timestamp', 'event_type', 'performed_by', 'description'],
            ['id', 'timestamp', 'event_type', 'performed_by', 'description', 'status'],
            ['id', 'timestamp', 'event_type', 'performed_by', 'description', 'status', 'details']
        ]
        
        working_columns = []
        
        for columns in test_columns:
            try:
                column_str = ', '.join(columns)
                result = supabase.table('audit_logs').select(column_str).limit(1).execute()
                working_columns = columns
                logger.info(f"✓ Columns working: {columns}")
            except Exception as e:
                logger.info(f"✗ Columns failed: {columns} - {e}")
                break
        
        logger.info(f"Working columns: {working_columns}")
        
        # Try to insert a test record to see what's missing
        test_record = {
            'timestamp': '2025-07-20T15:40:00',
            'event_type': 'Test',
            'performed_by': 'System',
            'description': 'Test description',
            'status': 'Success',
            'details': {}
        }
        
        try:
            result = supabase.table('audit_logs').insert(test_record).execute()
            logger.info("✓ Test insert successful - table schema is correct")
            
            # Clean up test record
            if result.data:
                delete_result = supabase.table('audit_logs').delete().eq('event_type', 'Test').execute()
                logger.info("Test record cleaned up")
            
            return True
        except Exception as e:
            logger.error(f"✗ Test insert failed: {e}")
            return False
        
    except Exception as e:
        logger.error(f"Error checking audit_logs table: {e}")
        return False

def main():
    """Main function."""
    logger.info("=== Checking Audit Logs Table Schema ===")
    
    success = check_audit_logs_table()
    
    if success:
        logger.info("✓ Audit logs table schema is correct")
    else:
        logger.error("✗ Audit logs table schema needs to be fixed")
        logger.info("Please run the following SQL in Supabase SQL Editor:")
        logger.info("""
-- Fix audit_logs table schema
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS event_type VARCHAR(100);
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS performed_by VARCHAR(255);
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'Success';
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS details JSONB DEFAULT '{}';

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_performed_by ON audit_logs(performed_by);
CREATE INDEX IF NOT EXISTS idx_audit_logs_status ON audit_logs(status);
        """)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
