{% extends "base.html" %}

{% block title %}Equipment History - {{ equipment.Name or equipment.EQUIPMENT or 'Unknown' }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Equipment History
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('views.add_equipment_history', equipment_type=equipment_type, equipment_id=equipment_id|url_safe_serial) }}"
                               class="btn btn-light btn-sm">
                                <i class="fas fa-plus"></i> Add History Note
                            </a>
                            <a href="{{ url_for('views.list_equipment', data_type=equipment_type) }}" 
                               class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary">Equipment Details</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Serial Number:</strong></td>
                                    <td>{{ equipment.SERIAL if equipment_type == 'ppm' else equipment.Serial }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ equipment.Name or equipment.EQUIPMENT or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Model:</strong></td>
                                    <td>{{ equipment.MODEL or equipment.Model or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Manufacturer:</strong></td>
                                    <td>{{ equipment.MANUFACTURER or equipment.Manufacturer or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Department:</strong></td>
                                    <td>{{ equipment.Department }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary">History Summary</h5>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h3 class="text-primary mb-0">{{ history_notes|length }}</h3>
                                            <small class="text-muted">Total Notes</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h3 class="text-success mb-0">
                                                {{ history_notes|selectattr('attachments')|list|length }}
                                            </h3>
                                            <small class="text-muted">With Attachments</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Timeline -->
    <div class="row">
        <div class="col-12">
            {% if history_notes %}
                <div class="timeline">
                    {% for note in history_notes %}
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <i class="fas fa-sticky-note text-primary"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="card shadow-sm">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">
                                            <i class="fas fa-user me-1"></i>
                                            {{ note.author_name }}
                                            {% if note.is_edited %}
                                                <span class="badge bg-warning text-dark ms-2">
                                                    <i class="fas fa-edit me-1"></i>Edited
                                                </span>
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ note.created_at }}
                                            {% if note.updated_at and note.updated_at != note.created_at %}
                                                <br><i class="fas fa-edit me-1"></i>
                                                Last edited: {{ note.updated_at }}
                                                {% if note.last_modified_by_name %}
                                                    by {{ note.last_modified_by_name }}
                                                {% endif %}
                                            {% endif %}
                                        </small>
                                    </div>
                                    {% if current_user.is_authenticated %}
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false"
                                                title="Note Actions">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <a class="dropdown-item"
                                                   href="{{ url_for('views.edit_history_note', note_id=note.id) }}">
                                                    <i class="fas fa-edit me-1"></i> Edit Note
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger"
                                                        onclick="deleteHistoryNote('{{ note.id }}')">
                                                    <i class="fas fa-trash me-1"></i> Delete Note
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="card-body">
                                    <p class="mb-3">{{ note.note_text }}</p>
                                    
                                    {% if note.attachments %}
                                    <div class="attachments-section">
                                        <h6 class="text-secondary mb-2">
                                            <i class="fas fa-paperclip me-1"></i>
                                            Attachments ({{ note.attachments|length }})
                                        </h6>
                                        <div class="row">
                                            {% for attachment in note.attachments %}
                                            <div class="col-md-6 col-lg-4 mb-2">
                                                <div class="attachment-item p-2 border rounded">
                                                    <div class="d-flex align-items-center">
                                                        <div class="file-icon me-2">
                                                            {% if attachment.mime_type.startswith('image/') %}
                                                                <i class="fas fa-image text-success"></i>
                                                            {% elif attachment.mime_type == 'application/pdf' %}
                                                                <i class="fas fa-file-pdf text-danger"></i>
                                                            {% elif 'word' in attachment.mime_type %}
                                                                <i class="fas fa-file-word text-primary"></i>
                                                            {% else %}
                                                                <i class="fas fa-file text-secondary"></i>
                                                            {% endif %}
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <div class="file-name small fw-bold">
                                                                {{ attachment.original_filename }}
                                                            </div>
                                                            <div class="file-size text-muted small">
                                                                {{ (attachment.file_size / 1024) | round(1) }} KB
                                                            </div>
                                                        </div>
                                                        <div class="file-actions">
                                                            <a href="/api/history/attachment/{{ attachment.id }}/download" 
                                                               class="btn btn-sm btn-outline-primary" 
                                                               title="Download">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No History Records</h5>
                            <p class="text-muted mb-4">This equipment doesn't have any history notes yet.</p>
                            <a href="{{ url_for('views.add_equipment_history', equipment_type=equipment_type, equipment_id=equipment_id|url_safe_serial) }}"
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First History Note
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this history note? This action cannot be undone.</p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> All attachments will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: white;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.timeline-content {
    margin-left: 20px;
}

.attachment-item {
    transition: all 0.2s ease;
}

.attachment-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff !important;
}

.file-icon {
    font-size: 1.2em;
}
</style>
{% endblock %}

{% block scripts %}
<script>
let noteToDelete = null;

function deleteHistoryNote(noteId) {
    noteToDelete = noteId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (noteToDelete) {
        // Show loading state
        const deleteBtn = document.getElementById('confirmDeleteBtn');
        const originalText = deleteBtn.innerHTML;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
        deleteBtn.disabled = true;

        fetch(`/api/history/${noteToDelete}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message briefly before reload
                deleteBtn.innerHTML = '<i class="fas fa-check me-1"></i>Deleted!';
                deleteBtn.classList.remove('btn-danger');
                deleteBtn.classList.add('btn-success');

                setTimeout(() => {
                    location.reload(); // Refresh the page to show updated history
                }, 1000);
            } else {
                // Reset button state
                deleteBtn.innerHTML = originalText;
                deleteBtn.disabled = false;

                // Show error message
                alert('Failed to delete history note: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Reset button state
            deleteBtn.innerHTML = originalText;
            deleteBtn.disabled = false;

            alert('Failed to delete history note. Please try again.');
        });

        // Don't hide modal immediately - let the success/error handling do it
    }
});

// Reset modal state when it's hidden
document.getElementById('deleteModal').addEventListener('hidden.bs.modal', function() {
    const deleteBtn = document.getElementById('confirmDeleteBtn');
    deleteBtn.innerHTML = 'Delete';
    deleteBtn.disabled = false;
    deleteBtn.classList.remove('btn-success');
    deleteBtn.classList.add('btn-danger');
    noteToDelete = null;
});

// Add loading states for edit buttons
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for edit links to show loading state
    const editLinks = document.querySelectorAll('a[href*="/history/"][href*="/edit"]');
    editLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const icon = this.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-spinner fa-spin me-1';
            }
            this.innerHTML = this.innerHTML.replace('Edit Note', 'Loading...');
        });
    });
});
</script>
{% endblock %}
