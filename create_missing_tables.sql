-- Create missing tables for ALORF BIOMED final migration
-- Run these commands in the Supabase SQL Editor

-- 1. Create settings table
CREATE TABLE IF NOT EXISTS settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for settings
CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);
CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category);
CREATE INDEX IF NOT EXISTS idx_settings_updated_at ON settings(updated_at);

-- 2. Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(100) NOT NULL DEFAULT 'Viewer',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for users
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);

-- 3. Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    permissions JSONB NOT NULL DEFAULT '[]',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for roles
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);

-- 4. Update audit_logs table to add missing columns
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS details JSONB DEFAULT '{}';

-- 5. Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers
DROP TRIGGER IF EXISTS update_settings_updated_at ON settings;
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. Insert default roles
INSERT INTO roles (name, permissions, description) VALUES
('Admin', '["dashboard_view", "equipment_ppm_read", "equipment_ppm_write", "equipment_ppm_delete", "equipment_ppm_import_export", "equipment_ocm_read", "equipment_ocm_write", "equipment_ocm_delete", "equipment_ocm_import_export", "training_manage", "training_read", "training_write", "training_delete", "audit_log_view", "audit_log_export", "user_manage", "settings_read", "settings_manage", "settings_email_test", "backup_manage", "import_data", "export_data"]'::jsonb, 'Full system access'),
('Editor', '["dashboard_view", "equipment_ppm_read", "equipment_ppm_write", "equipment_ppm_delete", "equipment_ppm_import_export", "equipment_ocm_read", "equipment_ocm_write", "equipment_ocm_delete", "equipment_ocm_import_export", "training_manage", "training_read", "training_write", "training_delete", "import_data", "export_data"]'::jsonb, 'Equipment and training management'),
('Viewer', '["dashboard_view", "equipment_ppm_read", "equipment_ocm_read"]'::jsonb, 'Read-only access')
ON CONFLICT (name) DO UPDATE SET
    permissions = EXCLUDED.permissions,
    description = EXCLUDED.description;

-- 7. Enable RLS and create policies
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

-- Create policies for service key access
DROP POLICY IF EXISTS "Service key full access" ON settings;
CREATE POLICY "Service key full access" ON settings FOR ALL USING (true);

DROP POLICY IF EXISTS "Service key full access" ON users;
CREATE POLICY "Service key full access" ON users FOR ALL USING (true);

DROP POLICY IF EXISTS "Service key full access" ON roles;
CREATE POLICY "Service key full access" ON roles FOR ALL USING (true);

-- 8. Add comments for documentation
COMMENT ON TABLE settings IS 'Application configuration settings stored as key-value pairs with JSONB values';
COMMENT ON TABLE users IS 'User accounts with authentication and role information';
COMMENT ON TABLE roles IS 'Role definitions with associated permissions';

COMMENT ON COLUMN settings.key IS 'Unique setting identifier (e.g., email_notifications_enabled)';
COMMENT ON COLUMN settings.value IS 'Setting value stored as JSONB for flexibility';
COMMENT ON COLUMN settings.category IS 'Setting category for organization (email, notifications, etc.)';

COMMENT ON COLUMN users.username IS 'Unique username for authentication';
COMMENT ON COLUMN users.password_hash IS 'Hashed password for security';
COMMENT ON COLUMN users.role IS 'User role (Admin, Editor, Viewer)';

COMMENT ON COLUMN roles.name IS 'Role name (Admin, Editor, Viewer)';
COMMENT ON COLUMN roles.permissions IS 'Array of permissions for this role';
