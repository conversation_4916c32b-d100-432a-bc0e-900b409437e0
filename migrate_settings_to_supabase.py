#!/usr/bin/env python3
"""
Migrate settings from JSON to Supabase
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.supabase_settings_service import SupabaseSettingsService
from app.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('settings_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def migrate_settings():
    """Migrate settings from JSON to Supabase."""
    try:
        logger.info("=== Settings Migration to Supabase Started ===")
        
        # Initialize the Supabase settings service
        settings_service = SupabaseSettingsService()
        
        # Find the settings file - try multiple paths
        settings_paths = [
            Path('app/data/settings.json'),
            Path('data/settings.json'),
            Path(Config.SETTINGS_JSON_PATH)
        ]
        
        settings_data = None
        settings_path = None
        
        for path in settings_paths:
            if path.exists():
                settings_path = path
                logger.info(f"Found settings file: {path}")
                with open(path, 'r') as f:
                    settings_data = json.load(f)
                break
        
        if settings_data is None:
            logger.error("No settings file found")
            return False
        
        logger.info(f"Loaded settings data with {len(settings_data)} keys")
        
        # Check if settings already exist in Supabase
        existing_settings = settings_service.load_settings()
        if len(existing_settings) > len(settings_service._get_default_settings()):
            logger.info("Settings already exist in Supabase, skipping migration")
            return True
        
        # Migrate the settings
        success = settings_service.migrate_from_json(settings_data.copy())
        
        if success:
            logger.info("Settings migration completed successfully")
            
            # Verify the migration
            migrated_settings = settings_service.load_settings()
            logger.info(f"Verification: Loaded {len(migrated_settings)} settings from Supabase")
            
            # Test specific settings
            test_keys = ['email_notifications_enabled', 'recipient_email', 'scheduler_interval_hours']
            for key in test_keys:
                if key in settings_data:
                    original_value = settings_data[key]
                    migrated_value = migrated_settings.get(key)
                    if original_value == migrated_value:
                        logger.info(f"✓ Setting '{key}' migrated correctly: {migrated_value}")
                    else:
                        logger.warning(f"⚠ Setting '{key}' mismatch - Original: {original_value}, Migrated: {migrated_value}")
            
            return True
        else:
            logger.error("Settings migration failed")
            return False
        
    except Exception as e:
        logger.error(f"Settings migration failed with error: {e}")
        return False

def test_settings_service():
    """Test the settings service functionality."""
    try:
        logger.info("=== Testing Settings Service ===")
        
        settings_service = SupabaseSettingsService()
        
        # Test loading settings
        settings = settings_service.load_settings()
        logger.info(f"Loaded {len(settings)} settings")
        
        # Test getting a specific setting
        email_enabled = settings_service.get_setting('email_notifications_enabled', False)
        logger.info(f"Email notifications enabled: {email_enabled}")
        
        # Test setting a value
        test_key = 'test_migration_setting'
        test_value = 'migration_test_value'
        
        if settings_service.set_setting(test_key, test_value, 'test'):
            logger.info(f"Successfully set test setting: {test_key} = {test_value}")
            
            # Verify the setting was saved
            retrieved_value = settings_service.get_setting(test_key)
            if retrieved_value == test_value:
                logger.info("✓ Test setting retrieved correctly")
                
                # Clean up test setting
                if settings_service.delete_setting(test_key):
                    logger.info("✓ Test setting deleted successfully")
                else:
                    logger.warning("⚠ Failed to delete test setting")
            else:
                logger.warning(f"⚠ Test setting mismatch - Expected: {test_value}, Got: {retrieved_value}")
        else:
            logger.error("Failed to set test setting")
        
        logger.info("Settings service test completed")
        return True
        
    except Exception as e:
        logger.error(f"Settings service test failed: {e}")
        return False

def main():
    """Main function."""
    try:
        # Step 1: Migrate settings
        if not migrate_settings():
            logger.error("Settings migration failed")
            return False
        
        # Step 2: Test the service
        if not test_settings_service():
            logger.error("Settings service test failed")
            return False
        
        logger.info("=== Settings Migration and Testing Completed Successfully ===")
        return True
        
    except Exception as e:
        logger.error(f"Migration process failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
