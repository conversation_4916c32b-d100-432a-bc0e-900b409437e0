{"timestamp":"2025-07-02T17:27:32.116675Z","level":"ERROR","module":"app","message":"Test error message","function":"log_error","line":199,"thread":14732,"process":5464}
{"timestamp":"2025-07-02T18:13:54.768328Z","level":"ERROR","module":"app","message":"Error importing ppm data: 'utf-8' codec can't decode byte 0xa0 in position 24366: invalid start byte","function":"import_from_csv","line":437,"thread":8476,"process":20516,"request":{"method":"POST","url":"http://localhost:5001/import_equipment","endpoint":"views.import_equipment","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/import-export"}}
{"timestamp":"2025-07-02T18:22:50.960908Z","level":"ERROR","module":"app","message":"Error processing row 1: time data '2024-01-15' does not match format '%d/%m/%Y'","function":"import_from_csv","line":478,"thread":12144,"process":9880}
{"timestamp":"2025-07-02T18:22:50.961911Z","level":"ERROR","module":"app","message":"Error processing row 2: time data '2024-02-20' does not match format '%d/%m/%Y'","function":"import_from_csv","line":478,"thread":12144,"process":9880}
{"timestamp":"2025-07-02T18:22:50.967099Z","level":"ERROR","module":"app","message":"Error processing row 3: time data '2024-03-10' does not match format '%d/%m/%Y'","function":"import_from_csv","line":478,"thread":12144,"process":9880}
{"timestamp":"2025-07-02T18:22:51.034914Z","level":"ERROR","module":"app","message":"Error processing row 1: time data '2024-01-15' does not match format '%d/%m/%Y'","function":"import_from_csv","line":478,"thread":12144,"process":9880}
{"timestamp":"2025-07-02T18:22:51.035884Z","level":"ERROR","module":"app","message":"Error processing row 2: time data '2024-02-20' does not match format '%d/%m/%Y'","function":"import_from_csv","line":478,"thread":12144,"process":9880}
{"timestamp":"2025-07-02T18:22:51.038755Z","level":"ERROR","module":"app","message":"Error processing row 3: time data '2024-03-10' does not match format '%d/%m/%Y'","function":"import_from_csv","line":478,"thread":12144,"process":9880}
{"timestamp":"2025-07-02T18:32:10.997239Z","level":"ERROR","module":"app","message":"Error processing row 84: time data '3/19/202' does not match format '%d/%m/%Y'","function":"import_from_csv","line":482,"thread":17968,"process":7120,"request":{"method":"POST","url":"http://localhost:5001/import_equipment","endpoint":"views.import_equipment","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/import-export"}}
{"timestamp":"2025-07-02T18:32:25.587822Z","level":"ERROR","module":"app.services.data_service","message":"Error decoding JSON from data\\ppm.json: Expecting ':' delimiter: line 41281 column 15 (char 925828). Returning empty list.","function":"load_data","line":183,"thread":13932,"process":7120,"request":{"method":"GET","url":"http://localhost:5001/equipment/ppm","endpoint":"views.list_equipment","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/import-export"}}
{"timestamp":"2025-07-02T21:40:53.598332Z","level":"ERROR","module":"app","message":"Error processing row 84: time data '3/19/202' does not match format '%d/%m/%Y'","function":"import_from_csv","line":482,"thread":20424,"process":9516,"request":{"method":"POST","url":"http://localhost:5001/import_equipment","endpoint":"views.import_equipment","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/import-export"}}
{"timestamp":"2025-07-04T00:29:03.741372Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":14072,"process":14704}
{"timestamp":"2025-07-04T00:29:03.745276Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":14072,"process":14704}
{"timestamp":"2025-07-04T00:37:33.715544Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":19072,"process":1832}
{"timestamp":"2025-07-04T00:37:33.716519Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":19072,"process":1832}
{"timestamp":"2025-07-04T00:41:16.259283Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":18712,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:16.262183Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":18712,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:16.266088Z","level":"ERROR","module":"app.routes.api","message":"Failed to send test push notification","function":"send_test_push","line":657,"thread":18712,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:32.516328Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":19300,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:32.518339Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":19300,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:32.527146Z","level":"ERROR","module":"app.routes.api","message":"Failed to send test push notification","function":"send_test_push","line":657,"thread":19300,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:41.909546Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":2476,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:41.910490Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":2476,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:41:41.913437Z","level":"ERROR","module":"app.routes.api","message":"Failed to send test push notification","function":"send_test_push","line":657,"thread":2476,"process":1832,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referrer":"http://localhost:5001/settings"}}
{"timestamp":"2025-07-04T00:46:23.619590Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":18640,"process":10796}
{"timestamp":"2025-07-04T00:46:23.640131Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":18640,"process":10796}
{"timestamp":"2025-07-04T00:48:05.286080Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":19300,"process":18776}
{"timestamp":"2025-07-04T00:48:05.298765Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":19300,"process":18776}
{"timestamp":"2025-07-04T00:56:45.823199Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB: WebPushException: Push failed: 410 Gone\nResponse body:push subscription has unsubscribed or expired.\n, Response push subscription has unsubscribed or expired.\n","function":"send_push_notification","line":248,"thread":9856,"process":10556}
{"timestamp":"2025-07-04T00:56:45.835887Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/dBZ-BDZjjrE:APA91bHyYcbI87NFv2VSqB did not have a response object.","function":"send_push_notification","line":255,"thread":9856,"process":10556}
{"timestamp":"2025-07-04T01:04:09.152123Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/sample-test-endpoint: WebPushException: Push failed: 404 Not Found\nResponse body:A valid push subscription endpoint should be specified in the URL as such: https://fcm.googleapis.com/wp/dHIoDxE7Hdg:APA91bH1Zj0kNa...\n, Response A valid push subscription endpoint should be specified in the URL as such: https://fcm.googleapis.com/wp/dHIoDxE7Hdg:APA91bH1Zj0kNa...\n","function":"send_push_notification","line":248,"thread":18036,"process":1876}
{"timestamp":"2025-07-04T01:04:09.153099Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/sample-test-endpoint did not have a response object.","function":"send_push_notification","line":255,"thread":18036,"process":1876}
{"timestamp":"2025-07-04T01:04:24.399760Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/sample-test-endpoint: WebPushException: Push failed: 404 Not Found\nResponse body:A valid push subscription endpoint should be specified in the URL as such: https://fcm.googleapis.com/wp/dHIoDxE7Hdg:APA91bH1Zj0kNa...\n, Response A valid push subscription endpoint should be specified in the URL as such: https://fcm.googleapis.com/wp/dHIoDxE7Hdg:APA91bH1Zj0kNa...\n","function":"send_push_notification","line":248,"thread":18108,"process":10556,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"python-requests/2.32.4","referrer":null}}
{"timestamp":"2025-07-04T01:04:24.406593Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/sample-test-endpoint did not have a response object.","function":"send_push_notification","line":255,"thread":18108,"process":10556,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"python-requests/2.32.4","referrer":null}}
{"timestamp":"2025-07-04T01:04:24.418305Z","level":"ERROR","module":"app.routes.api","message":"Failed to send test push notification","function":"send_test_push","line":657,"thread":18108,"process":10556,"request":{"method":"POST","url":"http://localhost:5001/api/test-push","endpoint":"api.send_test_push","remote_addr":"127.0.0.1","user_agent":"python-requests/2.32.4","referrer":null}}
{"timestamp":"2025-07-04T01:05:41.522032Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException while sending to https://fcm.googleapis.com/fcm/send/sample-test-endpoint: WebPushException: Push failed: 404 Not Found\nResponse body:A valid push subscription endpoint should be specified in the URL as such: https://fcm.googleapis.com/wp/dHIoDxE7Hdg:APA91bH1Zj0kNa...\n, Response A valid push subscription endpoint should be specified in the URL as such: https://fcm.googleapis.com/wp/dHIoDxE7Hdg:APA91bH1Zj0kNa...\n","function":"send_push_notification","line":248,"thread":14212,"process":18592}
{"timestamp":"2025-07-04T01:05:41.522032Z","level":"ERROR","module":"app.services.push_notification_service","message":"WebPushException for https://fcm.googleapis.com/fcm/send/sample-test-endpoint did not have a response object.","function":"send_push_notification","line":255,"thread":14212,"process":18592}
{"timestamp":"2025-07-04T03:06:02.765685Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":17696,"process":16960}
{"timestamp":"2025-07-04T11:03:22.563068Z","level":"ERROR","module":"__main__","message":"❌ File still exists after deletion request","function":"test_flask_route_deletion","line":72,"thread":4752,"process":9260,"request":{"method":"POST","url":"http://localhost/backup/delete/full_backup_20250704_140321_972215.zip","endpoint":"views.delete_backup","remote_addr":"127.0.0.1","user_agent":"Werkzeug/3.1.3","referrer":null},"user":{"id":"admin","username":"admin","role":"Admin"}}
{"timestamp":"2025-07-04T11:03:22.564042Z","level":"ERROR","module":"__main__","message":"Error during Flask route testing: <ContextVar name='flask.request_ctx' at 0x00000231417A5B70>","function":"test_flask_route_deletion","line":79,"thread":4752,"process":9260}
{"timestamp":"2025-07-04T11:03:22.628076Z","level":"ERROR","module":"app.services.backup_service","message":"Error deleting backup : [WinError 5] Access is denied: 'C:\\\\ALORFBIOMED\\\\data\\\\backups\\\\full\\\\'","function":"delete_backup","line":573,"thread":4752,"process":9260}
{"timestamp":"2025-07-04T11:03:22.670043Z","level":"ERROR","module":"__main__","message":"❌ Delete with empty filename - Message doesn't contain expected patterns","function":"test_error_message_scenarios","line":131,"thread":4752,"process":9260}
{"timestamp":"2025-07-04T11:03:22.671019Z","level":"ERROR","module":"__main__","message":"   Expected patterns: ['not found']","function":"test_error_message_scenarios","line":132,"thread":4752,"process":9260}
{"timestamp":"2025-07-04T11:03:22.671995Z","level":"ERROR","module":"__main__","message":"   Actual message: Failed to delete backup '': [WinError 5] Access is denied: 'C:\\\\ALORFBIOMED\\\\data\\\\backups\\\\full\\\\'","function":"test_error_message_scenarios","line":133,"thread":4752,"process":9260}
{"timestamp":"2025-07-04T11:03:22.677851Z","level":"ERROR","module":"__main__","message":"❌ INTEGRATION ISSUES DETECTED - Further fixes needed","function":"main","line":170,"thread":4752,"process":9260}
{"timestamp":"2025-07-04T12:05:40.010799Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":15504,"process":4364}
{"timestamp":"2025-07-04T12:39:55.476165Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":17396,"process":13140}
{"timestamp":"2025-07-08T01:36:56.187668Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":18792,"process":14752}
{"timestamp":"2025-07-08T19:46:38.616531Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":9568,"process":10132}
{"timestamp":"2025-07-08T19:49:17.293746Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":22316,"process":12520}
{"timestamp":"2025-07-08T19:50:02.464636Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":18320,"process":8940}
{"timestamp":"2025-07-08T19:50:39.599030Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":22140,"process":17540}
{"timestamp":"2025-07-08T20:03:34.883205Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":8712,"process":15248}
{"timestamp":"2025-07-08T20:46:38.856484Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":9568,"process":10132}
{"timestamp":"2025-07-08T20:49:17.621958Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":22316,"process":12520}
{"timestamp":"2025-07-08T20:50:02.786570Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":18320,"process":8940}
{"timestamp":"2025-07-08T20:50:40.691635Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":22140,"process":17540}
{"timestamp":"2025-07-08T21:00:33.722756Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":15592,"process":1924}
{"timestamp":"2025-07-08T21:03:35.817138Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":8712,"process":15248}
{"timestamp":"2025-07-08T21:46:39.816032Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":9568,"process":10132}
{"timestamp":"2025-07-08T21:49:18.974406Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":22316,"process":12520}
{"timestamp":"2025-07-08T21:49:23.283233Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":7096,"process":18708}
{"timestamp":"2025-07-08T21:50:03.945464Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":18320,"process":8940}
{"timestamp":"2025-07-08T21:50:41.666212Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":22140,"process":17540}
{"timestamp":"2025-07-08T22:00:34.888951Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":15592,"process":1924}
{"timestamp":"2025-07-08T22:03:37.036817Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":8712,"process":15248}
{"timestamp":"2025-07-10T00:47:30.778466Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":12004,"process":19072}
{"timestamp":"2025-07-10T01:10:26.211515Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":2260,"process":3888}
{"timestamp":"2025-07-10T01:10:28.946767Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":1780,"process":21916}
{"timestamp":"2025-07-10T01:47:31.193323Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":12004,"process":19072}
{"timestamp":"2025-07-10T02:10:26.452962Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":2260,"process":3888}
{"timestamp":"2025-07-10T02:10:29.142231Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":1780,"process":21916}
{"timestamp":"2025-07-19T16:22:06.577112Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":632,"process":17692}
{"timestamp":"2025-07-19T16:22:08.717728Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":18012,"process":12580}
{"timestamp":"2025-07-19T16:39:22.316333Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":18720,"process":8856}
{"timestamp":"2025-07-19T16:39:25.608706Z","level":"ERROR","module":"app.services.push_notification_service","message":"VAPID_PRIVATE_KEY or VAPID_SUBJECT not configured. Cannot send push notifications.","function":"send_push_notification","line":198,"thread":5816,"process":2244}
{"timestamp":"2025-07-20T12:22:57.948543Z","level":"ERROR","module":"app.services.supabase_settings_service","message":"Failed to load settings from database: {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation \"public.settings\" does not exist'}","function":"load_settings","line":78,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:22:58.191199Z","level":"ERROR","module":"app.services.supabase_settings_service","message":"Failed to save settings to database: {}","function":"save_settings","line":116,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:22:58.424281Z","level":"ERROR","module":"app.services.supabase_settings_service","message":"Failed to load settings from database: {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation \"public.settings\" does not exist'}","function":"load_settings","line":78,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:22:58.424281Z","level":"ERROR","module":"__main__","message":"✗ Settings service test failed - setting not saved correctly","function":"test_settings_service","line":56,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:22:58.424281Z","level":"ERROR","module":"__main__","message":"✗ Settings Service test FAILED","function":"main","line":241,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:00.211559Z","level":"ERROR","module":"app.services.supabase_audit_service","message":"Failed to log audit event: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': \"Could not find the 'description' column of 'audit_logs' in the schema cache\"}","function":"log_event","line":86,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:02.328520Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to get push subscriptions: {'code': '42703', 'details': None, 'hint': None, 'message': 'column push_subscriptions.is_active does not exist'}","function":"get_all_subscriptions","line":104,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:02.608553Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to migrate push subscriptions from JSON: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': \"Could not find the 'is_active' column of 'push_subscriptions' in the schema cache\"}","function":"migrate_from_json","line":298,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:02.947449Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to get push subscriptions: {'code': '42703', 'details': None, 'hint': None, 'message': 'column push_subscriptions.is_active does not exist'}","function":"get_all_subscriptions","line":104,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:03.254913Z","level":"ERROR","module":"app.services.supabase_settings_service","message":"Failed to load settings from database: {'code': '42P01', 'details': None, 'hint': None, 'message': 'relation \"public.settings\" does not exist'}","function":"load_settings","line":78,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:03.503733Z","level":"ERROR","module":"app.services.supabase_audit_service","message":"Failed to log audit event: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': \"Could not find the 'description' column of 'audit_logs' in the schema cache\"}","function":"log_event","line":86,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:23:03.537739Z","level":"ERROR","module":"__main__","message":"=== 1 Tests Failed! Please Check the Issues Above ===","function":"main","line":265,"thread":908,"process":7988}
{"timestamp":"2025-07-20T12:42:19.450136Z","level":"ERROR","module":"app.services.supabase_audit_service","message":"Failed to log audit event: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': \"Could not find the 'event_type' column of 'audit_logs' in the schema cache\"}","function":"log_event","line":86,"thread":8652,"process":1352}
{"timestamp":"2025-07-20T12:42:21.882605Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to get push subscriptions: {'code': '42703', 'details': None, 'hint': None, 'message': 'column push_subscriptions.is_active does not exist'}","function":"get_all_subscriptions","line":104,"thread":8652,"process":1352}
{"timestamp":"2025-07-20T12:42:22.130317Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to migrate push subscriptions from JSON: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': \"Could not find the 'is_active' column of 'push_subscriptions' in the schema cache\"}","function":"migrate_from_json","line":298,"thread":8652,"process":1352}
{"timestamp":"2025-07-20T12:42:22.381433Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to get push subscriptions: {'code': '42703', 'details': None, 'hint': None, 'message': 'column push_subscriptions.is_active does not exist'}","function":"get_all_subscriptions","line":104,"thread":8652,"process":1352}
{"timestamp":"2025-07-20T12:42:22.970231Z","level":"ERROR","module":"app.services.supabase_audit_service","message":"Failed to log audit event: {'code': 'PGRST204', 'details': None, 'hint': None, 'message': \"Could not find the 'event_type' column of 'audit_logs' in the schema cache\"}","function":"log_event","line":86,"thread":8652,"process":1352}
{"timestamp":"2025-07-20T12:48:32.555684Z","level":"ERROR","module":"__main__","message":"Email scheduler test failed: 'EmailService' object has no attribute 'get_current_settings'","function":"test_email_scheduler","line":119,"thread":3352,"process":18876}
{"timestamp":"2025-07-20T12:48:32.556390Z","level":"ERROR","module":"__main__","message":"❌ Email scheduler test FAILED - Please check the issues above","function":"main","line":131,"thread":3352,"process":18876}
{"timestamp":"2025-07-20T12:59:28.735298Z","level":"ERROR","module":"app.services.supabase_push_service","message":"Failed to migrate push subscriptions from JSON: {'code': '42P10', 'details': None, 'hint': None, 'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification'}","function":"migrate_from_json","line":298,"thread":4160,"process":9288}
{"timestamp":"2025-07-20T14:40:44.481158Z","level":"ERROR","module":"__main__","message":"❌ User 'admin' not found in loaded data!","function":"test_login_functionality","line":199,"thread":17700,"process":9492}
{"timestamp":"2025-07-20T14:40:44.485147Z","level":"ERROR","module":"__main__","message":"❌ Authentication issues remain","function":"main","line":258,"thread":17700,"process":9492}
{"timestamp":"2025-07-20T14:47:47.968965Z","level":"ERROR","module":"__main__","message":"Flask app not accessible: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001AAFD4ABD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))","function":"test_flask_login","line":47,"thread":15556,"process":8388}
{"timestamp":"2025-07-20T14:47:47.973952Z","level":"ERROR","module":"__main__","message":"❌ Flask login test FAILED!","function":"main","line":110,"thread":15556,"process":8388}
{"timestamp":"2025-07-20T14:49:23.167956Z","level":"ERROR","module":"__main__","message":"❌ Flask login test FAILED!","function":"main","line":117,"thread":7284,"process":11104}
{"timestamp":"2025-07-20T17:03:51.327050Z","level":"ERROR","module":"app.services.push_notification_service","message":"Error during push notification data processing or sending: 'str' object has no attribute 'get'","function":"process_push_notifications","line":332,"thread":16492,"process":6032,"exception":{"type":"AttributeError","message":"'str' object has no attribute 'get'","traceback":["Traceback (most recent call last):\n","  File \"C:\\ALORFBIOMED\\app\\services\\push_notification_service.py\", line 307, in process_push_notifications\n    upcoming_ppm = await EmailService.get_upcoming_maintenance(ppm_data, data_type='ppm')\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n","  File \"C:\\ALORFBIOMED\\app\\services\\email_service.py\", line 204, in get_upcoming_maintenance\n    department = entry.get('Department', 'N/A')\n                 ^^^^^^^^^\n","AttributeError: 'str' object has no attribute 'get'\n"]}}
