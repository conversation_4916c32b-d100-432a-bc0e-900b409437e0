# 🚀 ALORF BIOMED Supabase Migration Plan

## 📊 Current System Analysis

**Current Architecture:**
- **Data Storage**: JSON files (no SQLite database)
- **Data Models**: Pydantic models for validation
- **Total Records**: ~2,000+ across all data files

**Data Files Inventory:**
- `ppm.json` - 1,000+ PPM equipment records
- `ocm.json` - 300+ OCM equipment records  
- `training.json` - 600+ training records
- `departments.json` - 27 departments
- `trainers.json` - 10 trainers
- `settings.json` - Application settings
- `audit_log.json` - Audit trail
- `equipment_history.json` - Equipment history notes

## 🎯 Migration Phases

### Phase 1: Supabase Setup & Configuration
**Duration**: 1-2 hours
**Tasks**:
1. Create Supabase project
2. Obtain database credentials
3. Configure connection strings
4. Test connectivity

### Phase 2: Schema Creation
**Duration**: 2-3 hours
**Tasks**:
1. Create all 8 required tables
2. Set up foreign key relationships
3. Create indexes for performance
4. Configure Row Level Security (RLS)

### Phase 3: Data Migration Scripts
**Duration**: 3-4 hours
**Tasks**:
1. Create migration scripts for each data type
2. Handle data transformation and validation
3. Implement error handling and rollback
4. Create data integrity checks

### Phase 4: Code Adaptation
**Duration**: 4-6 hours
**Tasks**:
1. Update database configuration
2. Replace JSON file operations with SQL queries
3. Update all service classes
4. Modify API endpoints

### Phase 5: Testing & Validation
**Duration**: 2-3 hours
**Tasks**:
1. Verify data integrity
2. Test all CRUD operations
3. Performance testing
4. User acceptance testing

## 📋 Database Schema

### 1. PPM Equipment Table
```sql
CREATE TABLE ppm_equipment (
    id SERIAL PRIMARY KEY,
    no INTEGER,
    department VARCHAR(50) NOT NULL,
    name VARCHAR(200),
    model VARCHAR(100) NOT NULL,
    serial VARCHAR(100) NOT NULL UNIQUE,
    manufacturer VARCHAR(100) NOT NULL,
    log_number VARCHAR(50) NOT NULL,
    installation_date VARCHAR(20),
    warranty_end VARCHAR(20),
    status VARCHAR(20) DEFAULT 'Upcoming',
    has_history BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. PPM Quarterly Data Table
```sql
CREATE TABLE ppm_quarters (
    id SERIAL PRIMARY KEY,
    ppm_equipment_id INTEGER REFERENCES ppm_equipment(id) ON DELETE CASCADE,
    quarter VARCHAR(10) NOT NULL,
    engineer VARCHAR(100),
    quarter_date VARCHAR(20),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. OCM Equipment Table
```sql
CREATE TABLE ocm_equipment (
    id SERIAL PRIMARY KEY,
    no INTEGER,
    department VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    model VARCHAR(100) NOT NULL,
    serial VARCHAR(100) NOT NULL UNIQUE,
    manufacturer VARCHAR(100) NOT NULL,
    log_number VARCHAR(50) NOT NULL,
    installation_date VARCHAR(20),
    warranty_end VARCHAR(20),
    service_date VARCHAR(20),
    engineer VARCHAR(100) NOT NULL,
    next_maintenance VARCHAR(20),
    status VARCHAR(20) DEFAULT 'Upcoming',
    has_history BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Training Records Table
```sql
CREATE TABLE training_records (
    id SERIAL PRIMARY KEY,
    employee_id VARCHAR(20),
    name VARCHAR(200),
    department VARCHAR(50),
    last_trained_date VARCHAR(20),
    next_due_date VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Machine Trainer Assignments Table
```sql
CREATE TABLE machine_trainer_assignments (
    id SERIAL PRIMARY KEY,
    training_record_id INTEGER REFERENCES training_records(id) ON DELETE CASCADE,
    machine VARCHAR(200),
    trainer VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 6. Departments Table
```sql
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    information TEXT,
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW()
);
```

### 7. Trainers Table
```sql
CREATE TABLE trainers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    department_id INTEGER REFERENCES departments(id),
    telephone VARCHAR(20),
    information TEXT,
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW()
);
```

### 8. Application Settings Table
```sql
CREATE TABLE application_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 Migration Scripts Required

### 1. Department Migration
- Source: `departments.json` (27 records)
- Target: `departments` table
- Key considerations: Preserve IDs for foreign key relationships

### 2. Trainer Migration  
- Source: `trainers.json` (10 records)
- Target: `trainers` table
- Dependencies: Departments table must exist first

### 3. PPM Equipment Migration
- Source: `ppm.json` (1,000+ records)
- Target: `ppm_equipment` + `ppm_quarters` tables
- Complex: Split quarterly data into separate table

### 4. OCM Equipment Migration
- Source: `ocm.json` (300+ records)
- Target: `ocm_equipment` table
- Straightforward: Direct mapping

### 5. Training Records Migration
- Source: `training.json` (600+ records)
- Target: `training_records` + `machine_trainer_assignments` tables
- Complex: Split machine assignments into separate table

## ⚠️ Critical Considerations

### Data Integrity
- Preserve all existing IDs
- Maintain foreign key relationships
- Handle NULL values appropriately
- Validate data during migration

### Performance
- Use batch inserts for large datasets
- Create indexes after data migration
- Monitor query performance

### Security
- Configure Row Level Security (RLS)
- Set up proper authentication
- Secure database credentials

### Rollback Plan
- Create full data backups before migration
- Document rollback procedures
- Test rollback scenarios

## 📈 Success Metrics

### Data Validation
- ✅ All records migrated successfully
- ✅ No data loss or corruption
- ✅ Foreign key relationships intact
- ✅ Application functions identically

### Performance Benchmarks
- ✅ Page load times ≤ current performance
- ✅ Database queries optimized
- ✅ No timeout issues

### Functional Testing
- ✅ All CRUD operations work
- ✅ Search and filtering functional
- ✅ Import/export features work
- ✅ Real-time synchronization active

## 🚀 Next Steps

1. **Setup Supabase Project** - Create account and project
2. **Configure Environment** - Update connection strings
3. **Create Schema** - Execute table creation scripts
4. **Migrate Data** - Run migration scripts systematically
5. **Update Code** - Adapt application for PostgreSQL
6. **Test Thoroughly** - Validate all functionality
7. **Deploy** - Switch to production Supabase database

**Estimated Total Time**: 12-18 hours
**Risk Level**: Medium (comprehensive testing required)
**Rollback Time**: 2-4 hours if needed
